import { defineMessages } from 'react-intl';

export const attachGiftToOrderDrawerMessages = defineMessages({
  title: {
    id: 'attachGiftToOrderDrawer.title',
    defaultMessage: 'Attach Gift to Order',
  },
  instructionsTitle: {
    id: 'attachGiftToOrderDrawer.instructionsTitle',
    defaultMessage: 'How to attach a gift:',
  },
  instructionStep1: {
    id: 'attachGiftToOrderDrawer.instructionStep1',
    defaultMessage:
      '1. Deposit your gift to the bot using the "Deposit a Gift" button',
  },
  instructionStep2: {
    id: 'attachGiftToOrderDrawer.instructionStep2',
    defaultMessage:
      '2. Click "Attach Gift to this Order" button and select the deposited gift',
  },
  selectGift: {
    id: 'attachGiftToOrderDrawer.selectGift',
    defaultMessage: 'Select a gift to attach',
  },
  noGiftsAvailable: {
    id: 'attachGiftToOrderDrawer.noGiftsAvailable',
    defaultMessage: 'No gifts available for this collection',
  },
  linkGiftToOrder: {
    id: 'attachGiftToOrderDrawer.linkGiftToOrder',
    defaultMessage: 'Link Gift to Order',
  },
  linking: {
    id: 'attachGiftToOrderDrawer.linking',
    defaultMessage: 'Linking...',
  },
  giftLinkedSuccessfully: {
    id: 'attachGiftToOrderDrawer.giftLinkedSuccessfully',
    defaultMessage: 'Gift linked to order successfully',
  },
  errorLoadingGifts: {
    id: 'attachGiftToOrderDrawer.errorLoadingGifts',
    defaultMessage: 'Error loading available gifts',
  },
  errorLinkingGift: {
    id: 'attachGiftToOrderDrawer.errorLinkingGift',
    defaultMessage: 'Error linking gift to order',
  },
});
