import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { useOrderGift } from '@/hooks/use-order-gift';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';

interface UserOrderImageSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
}

export function UserOrderImageSection({
  collection,
  order,
}: UserOrderImageSectionProps) {
  const { gift, loading } = useOrderGift(order);

  return (
    <div className="relative">
      <div
        className={cn(
          'aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] border border-[#3a4a5c]/50 p-8',
          gift && 'p-0',
        )}
      >
        {gift ? (
          <TgsOrImageGift
            isImage={false}
            gift={gift}
            className="w-full h-full"
            style={{ width: '100%', height: '100%' }}
          />
        ) : loading ? (
          <div className="w-full h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        ) : collection ? (
          <TgsOrImage
            isImage={false}
            collectionId={collection.id}
            imageProps={{
              alt: collection.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        ) : (
          <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
            <div className="w-16 h-16 bg-[#17212b] rounded" />
          </div>
        )}
      </div>
    </div>
  );
}
