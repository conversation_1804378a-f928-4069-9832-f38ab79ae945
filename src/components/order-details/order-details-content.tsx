'use client';

import { openTelegramLink, shareURL } from '@telegram-apps/sdk-react';
import { Share } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { makeSecondaryMarketPurchase } from '@/api/orders-api';
import { formatServerError } from '@/api/server-error-handler';
import { DepositDrawer } from '@/components/deposit-drawer';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { InsufficientBalance } from '@/components/ui/insufficient-balance';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import { preloadSingleGift } from '@/hooks/use-gift-preloader';
import { useOrderGift } from '@/hooks/use-order-gift';
import { roundToThreeDecimals } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';
import { OrderStatus, Role, UserType } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import {
  calculateBalanceValidation,
  getOrderDisplayNumber,
  isSecondaryMarketPurchaseEligible,
} from '@/services/order-service';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';
import { generateOrderShareLink } from '@/utils/order-deep-link-utils';

import { OrderTraitsSection } from '../shared/order-traits-section';
import { orderDetailsContentMessages } from './intl/order-details-content.messages';
import {
  OrderDetailsActionButtons,
  OrderDetailsFeesSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
} from './order-details-drawer/index';

interface OrderDetailsContentProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
  hideActionButton?: boolean;
  onClose?: () => void;
  showCloseButton?: boolean;
}

export function OrderDetailsContent({
  order,
  collection,
  userType,
  onOrderAction,
  hideActionButton = false,
  onClose,
  showCloseButton = false,
}: OrderDetailsContentProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, appConfig, role } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);

  const { hasSufficientBalance } = calculateBalanceValidation({
    order,
    currentUser,
    appConfig,
  });

  const effectiveUserType = userType || UserType.BUYER;

  const handleTopUp = () => {
    setShowDepositDrawer(true);
  };

  const handleAction = async () => {
    if (!order?.id || !currentUser?.id) return;

    setActionLoading(true);

    let result;

    if (isSecondaryMarketPurchaseEligible(order, currentUser)) {
      try {
        const purchaseResult = await makeSecondaryMarketPurchase(order.id);
        result = {
          success: purchaseResult.success,
          message: purchaseResult.message,
        };
        if (purchaseResult.success) {
          toast.success(
            purchaseResult.message ||
              'Secondary market purchase completed successfully!',
          );
        } else {
          toast.error(
            purchaseResult.message || 'Secondary market purchase failed',
          );
        }
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        const errorMessage = formatServerError(error, t);
        toast.error(errorMessage);
        result = { success: false, message: errorMessage };
      }
    } else {
      let actionUserType: UserType;

      if (order.buyerId && currentUser.id !== order.buyerId) {
        actionUserType = UserType.BUYER;
      } else if (order.sellerId && currentUser.id !== order.sellerId) {
        actionUserType = UserType.SELLER;
      } else {
        actionUserType = effectiveUserType;
      }

      result = await executeMarketplaceOrderAction(order.id, actionUserType, t);
    }

    if (result.success && onOrderAction) {
      onOrderAction();
    }
    setActionLoading(false);
  };

  const handleShowResellHistory = () => {
    setShowResellHistory(true);
  };

  const handleShare = async () => {
    setIsSharing(true);

    if (!order.id) {
      toast.error('Order ID not available');
      setIsSharing(false);
      return;
    }

    try {
      const shareLink = generateOrderShareLink(order.id);

      if (shareURL.isAvailable()) {
        shareURL(shareLink, 'Check out this order!');
      } else if (openTelegramLink.isAvailable()) {
        openTelegramLink(shareLink);
      } else {
        window.open(shareLink, '_blank');
      }
    } catch (error) {
      console.error('Error sharing order:', error);
      toast.error('Failed to share order');
    } finally {
      setIsSharing(false);
    }
  };

  // TODO: Temporarily hidden - resell tx history feature
  const shouldShowResellHistory = false;
  // order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  // New button logic based on requirements
  const shouldShowButton = () => {
    if (!currentUser?.id) return false;

    if (
      order.status === OrderStatus.ACTIVE &&
      order.buyerId &&
      currentUser.id !== order.buyerId
    ) {
      return true;
    }

    if (
      order.status === OrderStatus.ACTIVE &&
      order.sellerId &&
      currentUser.id !== order.sellerId
    ) {
      return true;
    }

    if (
      order.status === OrderStatus.PAID &&
      order.secondaryMarketPrice &&
      order.secondaryMarketPrice > 0 &&
      currentUser.id !== order.buyerId &&
      currentUser.id !== order.sellerId
    ) {
      return true;
    }

    return false;
  };

  const shouldHideActionButton = hideActionButton || !shouldShowButton();

  const getActionLabel = () => {
    if (
      order.status === OrderStatus.PAID &&
      order.secondaryMarketPrice &&
      order.secondaryMarketPrice > 0 &&
      currentUser?.id !== order.buyerId &&
      currentUser?.id !== order.sellerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.buy)}{' '}
            <span className="font-bold">
              {roundToThreeDecimals(order.secondaryMarketPrice)}
            </span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    if (
      order.status === OrderStatus.ACTIVE &&
      order.buyerId &&
      currentUser?.id !== order.buyerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.fulfill)}{' '}
            <span className="font-bold">
              {roundToThreeDecimals(order.price)}
            </span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    if (
      order.status === OrderStatus.ACTIVE &&
      order.sellerId &&
      currentUser?.id !== order.sellerId
    ) {
      return (
        <div className="text-lg flex items-center gap-1">
          <span>
            {t(orderDetailsContentMessages.buy)}{' '}
            <span className="font-bold">
              {roundToThreeDecimals(order.price)}
            </span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      );
    }

    return (
      <div className="text-lg flex items-center gap-1">
        <span>
          {t(orderDetailsContentMessages.action)}{' '}
          <span className="font-bold">{roundToThreeDecimals(order.price)}</span>
        </span>
        <TonLogo className="-ml-[5px]" size={24} />
      </div>
    );
  };

  const actionLabel = getActionLabel();

  const { gift } = useOrderGift(order);

  useEffect(() => {
    if (gift) {
      preloadSingleGift(gift);
    }
  }, [gift]);

  return (
    <div className="space-y-4">
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
        gift={gift}
      />

      <OrderDetailsHeaderSection collection={collection} />

      <div className="flex justify-center">
        <Button
          onClick={handleShare}
          disabled={isSharing}
          variant="outline"
          className="border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
        >
          {isSharing ? (
            <>
              <Share className="w-4 h-4 mr-2 animate-pulse" />
              {t(orderDetailsContentMessages.openingTelegram)}
            </>
          ) : (
            <>
              <Share className="w-4 h-4 mr-1" />
              Order {getOrderDisplayNumber(order)}
            </>
          )}
        </Button>
      </div>

      {gift && <OrderTraitsSection gift={gift} />}

      <SellPriceDetails order={order} />

      <OrderDetailsFeesSection order={order} />

      {/* <OrderDetailsDate updatedAt={order.updatedAt} /> */}

      {role === Role.ADMIN && (
        <OrderActors
          buyerId={order.buyerId}
          sellerId={order.sellerId}
          isResellOrder={
            order.status === OrderStatus.PAID &&
            Number(order?.secondaryMarketPrice) > 0
          }
          isOpen={true}
        />
      )}

      {!shouldHideActionButton ? (
        <>
          {!hasSufficientBalance && (
            <InsufficientBalance
              message={t(orderDetailsContentMessages.insufficientBalance)}
              onTopUp={handleTopUp}
              className="mb-4"
            />
          )}
          <OrderDetailsActionButtons
            primaryAction={{
              label: actionLabel,
              onClick: handleAction,
              loading: actionLoading,
              disabled: !hasSufficientBalance,
            }}
            secondaryAction={
              shouldShowResellHistory
                ? {
                    label: t(orderDetailsContentMessages.showResellHistory),
                    onClick: handleShowResellHistory,
                  }
                : undefined
            }
            shouldShowCloseButton={showCloseButton}
            onClose={onClose}
            actionLoading={actionLoading}
          />
        </>
      ) : showCloseButton && onClose ? (
        <div className="space-y-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
          >
            Close
          </Button>
        </div>
      ) : null}

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />
    </div>
  );
}
