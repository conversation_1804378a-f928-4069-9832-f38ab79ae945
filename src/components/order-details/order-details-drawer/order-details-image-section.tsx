import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderGift,
} from '@/mikerudenko/marketplace-shared';

interface OrderDetailsImageSectionProps {
  collectionId: string;
  collection: CollectionEntity | null;
  gift?: OrderGift | null;
}

export function OrderDetailsImageSection({
  collectionId,
  collection,
  gift,
}: OrderDetailsImageSectionProps) {
  return (
    <div className="relative">
      <div
        className={cn(
          'w-[50%] mx-auto aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] border border-[#3a4a5c]/50 p-8',
          gift && 'p-0',
        )}
      >
        {gift ? (
          <TgsOrImageGift
            isImage={false}
            gift={gift}
            className="w-full h-full"
            style={{ width: '100%', height: '100%' }}
          />
        ) : (
          <TgsOrImage
            isImage={false}
            collectionId={collectionId}
            imageProps={{
              alt: collection?.name || 'Order item',
              fill: true,
              className: 'object-contain drop-shadow-2xl',
            }}
            tgsProps={{
              style: { height: '100%', width: '100%' },
            }}
          />
        )}
      </div>
    </div>
  );
}
