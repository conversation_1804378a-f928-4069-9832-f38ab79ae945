'use client';

import { <PERSON>R<PERSON>, Share, X } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { useLocalStorage } from 'usehooks-ts';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import { AppLocale, LocalStorageKeys } from '@/core.constants';
import { useRootContext } from '@/root-context';
import { generateReferralLink } from '@/utils/referral-utils';

import { welcomeModalMessages } from './welcome-modal.messages';

const LANGUAGE_LABELS = {
  [AppLocale.en]: 'English',
  [AppLocale.ru]: 'Русский',
  [AppLocale.ua]: 'Українська',
};

const getUserCountry = (): string => {
  if (typeof window === 'undefined') return 'unknown';

  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const locale = navigator.language || navigator.languages?.[0] || '';

    if (
      timezone.includes('Kiev') ||
      timezone.includes('Kyiv') ||
      locale.toLowerCase().includes('ua')
    ) {
      return 'UA';
    }

    if (
      timezone.includes('Moscow') ||
      timezone.includes('Europe/Moscow') ||
      locale.toLowerCase().startsWith('ru')
    ) {
      return 'RU';
    }

    if (timezone.includes('Minsk') || locale.toLowerCase().includes('by')) {
      return 'BY';
    }

    return 'OTHER';
  } catch {
    return 'OTHER';
  }
};

const getAvailableLanguages = (country: string): AppLocale[] => {
  switch (country) {
    case 'UA':
      return [AppLocale.en, AppLocale.ua];
    case 'RU':
    case 'BY':
      return [AppLocale.en, AppLocale.ru];
    default:
      return [AppLocale.en];
  }
};

interface WelcomeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WelcomeModal({ open, onOpenChange }: WelcomeModalProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, locale, setLocale } = useRootContext();
  const [, setWelcomeModalShown] = useLocalStorage(
    LocalStorageKeys.WELCOME_MODAL_SHOWN,
    false,
  );

  const [step, setStep] = useState(1);
  const [isSharing, setIsSharing] = useState(false);

  const userCountry = getUserCountry();
  const availableLanguages = getAvailableLanguages(userCountry);
  const shouldShowLanguageStep = availableLanguages.length > 1;

  const totalSteps = shouldShowLanguageStep ? 2 : 1;
  const currentStepForDisplay = shouldShowLanguageStep ? step : 2;

  const handleLanguageSelect = (selectedLocale: AppLocale) => {
    setLocale(selectedLocale);
    if (shouldShowLanguageStep) {
      setStep(2);
    }
  };

  const handleShareReferralLink = async () => {
    if (!currentUser?.id) return;

    setIsSharing(true);
    const referralLink = generateReferralLink(currentUser.id);

    try {
      if (
        navigator.share &&
        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent,
        )
      ) {
        await navigator.share({
          title: t(welcomeModalMessages.shareTitle),
          text: t(welcomeModalMessages.shareText),
          url: referralLink,
        });
        toast.success(t(welcomeModalMessages.linkSharedSuccessfully));
      } else {
        await navigator.clipboard.writeText(referralLink);
        toast.success(t(welcomeModalMessages.linkCopiedToClipboard));
      }
    } catch (error) {
      console.error('Error sharing referral link:', error);
      try {
        await navigator.clipboard.writeText(referralLink);
        toast.success(t(welcomeModalMessages.linkCopiedToClipboard));
      } catch {
        toast.error(t(welcomeModalMessages.failedToShareLink));
      }
    } finally {
      setIsSharing(false);
    }
  };

  const handleClose = () => {
    setWelcomeModalShown(true);
    onOpenChange(false);
  };

  const handleNext = () => {
    setWelcomeModalShown(true);
    onOpenChange(false);
  };

  const renderLanguageStep = () => (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <DialogTitle className="text-xl font-bold text-[#f5f5f5]">
          {t(welcomeModalMessages.selectLanguage)}
        </DialogTitle>
        <DialogDescription className="text-[#708499]">
          {t(welcomeModalMessages.choosePreferredLanguage)}
        </DialogDescription>
      </div>

      <div className="space-y-3">
        {availableLanguages.map((lang) => (
          <Button
            key={lang}
            onClick={() => handleLanguageSelect(lang)}
            variant="outline"
            className={`w-full h-12 text-left justify-start bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] text-[#f5f5f5] ${
              locale === lang ? 'border-[#0098EA] bg-[#0098EA]/10' : ''
            }`}
          >
            {LANGUAGE_LABELS[lang]}
          </Button>
        ))}
      </div>
    </div>
  );

  const renderReferralStep = () => (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <DialogTitle className="text-xl font-bold text-[#f5f5f5]">
          {t(welcomeModalMessages.earnRewards)}
        </DialogTitle>
        <DialogDescription className="text-[#708499]">
          {t(welcomeModalMessages.shareAppDescription)}
        </DialogDescription>
      </div>

      <div className="bg-[#232e3c] rounded-lg p-4 border border-[#3a4a5c]">
        <Button
          onClick={handleShareReferralLink}
          disabled={isSharing || !currentUser?.id}
          className="w-full bg-[#0098EA] hover:bg-[#0088d4] text-white h-12"
        >
          {isSharing ? (
            <>
              <Share className="w-4 h-4 mr-2 animate-pulse" />
              {t(welcomeModalMessages.sharing)}
            </>
          ) : (
            <>
              <Share className="w-4 h-4 mr-2" />
              {t(welcomeModalMessages.shareReferralLink)}
            </>
          )}
        </Button>
      </div>

      <div className="flex gap-3">
        <Button
          onClick={handleClose}
          variant="outline"
          className="flex-1 bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] text-[#f5f5f5]"
        >
          {t(welcomeModalMessages.skip)}
        </Button>
        <Button
          onClick={handleNext}
          className="flex-1 bg-[#0098EA] hover:bg-[#0088d4] text-white"
        >
          {t(welcomeModalMessages.iSharedIt)}
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md bg-[#17212b] border-[#3a4a5c] p-0 gap-0 [&>button:last-child]:hidden">
        <div className="relative p-6">
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none text-[#708499] hover:text-[#f5f5f5]"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          {shouldShowLanguageStep && step === 1
            ? renderLanguageStep()
            : renderReferralStep()}

          {totalSteps > 1 && (
            <div className="flex justify-center space-x-1.5 mt-6">
              {[...Array(totalSteps)].map((_, index) => (
                <div
                  key={index}
                  className={`h-1.5 w-1.5 rounded-full transition-colors ${
                    index + 1 === currentStepForDisplay
                      ? 'bg-[#0098EA]'
                      : 'bg-[#3a4a5c]'
                  }`}
                />
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
