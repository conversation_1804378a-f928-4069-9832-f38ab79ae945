import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

import { CDN_BASE_URL } from '@/core.constants';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getImagePathWithFallback = (
  collectionId: string,
  format: 'png' | 'tgs' = 'png',
) => {
  if (!collectionId) return { primary: '', fallback: '' };

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}/Original.${format}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  return {
    primary: cdnUrl,
    fallback: localPath,
  };
};

export function isUserPidor(): boolean {
  if (typeof window === 'undefined') {
    return false; // SSR safety
  }

  const locale = navigator.language || navigator.languages?.[0] || '';

  return locale.toLowerCase().startsWith('ru');
}

export function roundToThreeDecimals(value?: number | null) {
  if (value === null || value === undefined) {
    return 0;
  }

  return Math.round(value * 1000) / 1000;
}

export function safeMultiply(amount: number, multiplier: number) {
  return roundToThreeDecimals(amount * multiplier);
}

export function bpsToDecimal(bps: number): number {
  return bps / 10000;
}
