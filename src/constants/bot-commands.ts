export enum BotCommands {
  START = "start",
  HELP = "help",
  HEALTH = "health",
}

export enum ButtonMessageIds {
  MY_GIFTS = "buttons.myGifts",
  MY_SELL_ORDERS = "buttons.mySellOrders",
  DEPOSIT_GIFT = "buttons.depositGift",
  CONTACT_SUPPORT = "buttons.contactSupport",
}

export enum CallbackActions {
  ORDER_HELP = "order_help",
  CONTACT_SUPPORT = "contact_support",
  OPEN_MARKETPLACE = "open_marketplace",
  BACK_TO_ORDERS = "back_to_orders",
  VIEW_BUY_ORDERS = "view_buy_orders",
  VIEW_SELL_ORDERS = "view_sell_orders",
  PAID_ORDERS_AWAITING_GIFT = "paid_orders_awaiting_gift",
  CREATED_ORDERS_NEEDING_ACTIVATION = "created_orders_needing_activation",
  CANCELLED_ORDERS_WITH_GIFTS = "cancelled_orders_with_gifts",
  BACK_TO_MENU = "back_to_menu",
}

export enum CallbackPatterns {
  ORDER_SELECTION = "^order_(.+)$",
  ORDER_COMPLETION = "^complete_(.+)$",
  RECEIVE_GIFT = "^receive_gift_(.+)$",
}

export enum CallbackPrefixes {
  ORDER = "order_",
  COMPLETE = "complete_",
  RECEIVE_GIFT = "receive_gift_",
}
