import { log } from "../utils/logger";

export function logServerReadiness(params: { ready: boolean }) {
  log.info(`Server readiness: ${params.ready ? "READY" : "NOT READY"}`, {
    operation: "http_server",
    ready: params.ready,
  });
}

export function logHttpRequest(params: { method: string; url: string }) {
  log.info(`${params.method} ${params.url}`, {
    operation: "http_request",
    method: params.method,
    url: params.url,
  });
}

export function logHealthcheckError(params: { error: unknown }) {
  log.error("Error in healthcheck endpoint", params.error, {
    operation: "healthcheck",
  });
}

export function logWebhookProcessingError(params: { error: unknown }) {
  log.error("Error processing webhook update ", params.error, {
    operation: "webhook_processing",
  });
}

export function logRequestHandlingError(params: {
  error: Error;
  url: string;
  method: string;
}) {
  log.error("Request handling error ", params.error, {
    operation: "http_request",
    url: params.url,
    method: params.method,
  });
}

export function logServerStarted(params: { port: number }) {
  log.info(`Express HTTP server running on port ${params.port}`, {
    operation: "http_server_start",
    port: params.port,
    healthcheckUrl: `http://localhost:${params.port}/healthcheck`,
  });
}

export function logServerError(params: { error: Error; port: number }) {
  log.error("HTTP server error ", params.error, {
    operation: "http_server_start",
    port: params.port,
  });
}

export function logServerStopping() {
  log.info("Stopping Express HTTP server", {
    operation: "http_server_stop",
  });
}

export function logServerStopped() {
  log.info("Express HTTP server stopped", {
    operation: "http_server_stop",
    status: "completed",
  });
}

export function logWebhookReceived(params: { updateId?: number }) {
  log.webhookLog("Received webhook update - ", {
    operation: "webhook_processing",
    updateId: params.updateId,
  });
}
