import type { Timestamp } from 'firebase/firestore';
import type { IntlShape } from 'react-intl';

import { FREEZE_PERIOD_DAYS } from '@/core.constants';
import { bpsToDecimal } from '@/lib/utils';
import type {
  AppConfigEntity,
  CollectionEntity,
  OrderEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import {
  firebaseTimestampToDate,
  OrderStatus,
  UserType,
} from '@/mikerudenko/marketplace-shared';
import { orderStatusUtilsMessages } from '@/utils/intl/order-status-utils.messages';

export interface CollateralCalculation {
  sellerLockedAmount: number;
  buyerLockedAmount: number;
  purchaseFeeAmount: number;
  resellPurchaseFeeAmount: number;
}

export interface BalanceValidationResult {
  hasSufficientBalance: boolean;
  requiredAmount: number;
}

export interface TimerState {
  timeLeft: string;
  isFreezed: boolean;
}

export interface DeadlineInfo {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  isExpired: boolean;
}

export function formatBPSToPercent(
  bps: number,
  decimalPlaces: number = 1,
): string {
  return (bps / 100).toFixed(decimalPlaces);
}

export function calculateCollateralLoss(
  order: OrderEntity | null,
  currentUser: UserEntity | null,
): number {
  if (!order || !currentUser) return 0;

  if (order.status === OrderStatus.PAID) {
    if (currentUser.id === order.buyerId) {
      return order.price; // Buyer loses full payment
    } else if (currentUser.id === order.sellerId) {
      const sellerLockPercentage =
        (order.fees?.seller_locked_percentage ?? 0) / 10000;
      return order.price * sellerLockPercentage; // Seller loses locked collateral
    }
  }
  return 0;
}

export function isOriginalSeller(
  currentUser: UserEntity | null,
  order: OrderEntity | null,
): boolean {
  return currentUser?.id === order?.sellerId;
}

export function hasResellerEarnings(order: OrderEntity | null): boolean {
  return !!(
    order?.reseller_earnings_for_seller &&
    order.reseller_earnings_for_seller > 0
  );
}

export function getUserLabel(userType: UserType): string {
  return userType === 'seller' ? 'Buyer' : 'Seller';
}

export function calculateOrderAmounts(
  order: OrderEntity,
  showPurchaseFee: boolean = false,
  hasSecondaryPrice: boolean = false,
): CollateralCalculation {
  const fees = order.fees;
  if (!fees) {
    return {
      sellerLockedAmount: 0,
      buyerLockedAmount: 0,
      purchaseFeeAmount: 0,
      resellPurchaseFeeAmount: 0,
    };
  }

  const sellerLockedAmount =
    order.price * bpsToDecimal(fees.seller_locked_percentage);
  const buyerLockedAmount =
    order.price * bpsToDecimal(fees.buyer_locked_percentage);
  const purchaseFeeAmount = showPurchaseFee
    ? order.price * bpsToDecimal(fees.purchase_fee)
    : 0;
  const resellPurchaseFeeAmount = hasSecondaryPrice
    ? (order.secondaryMarketPrice || 0) * bpsToDecimal(fees.resell_purchase_fee)
    : 0;

  return {
    sellerLockedAmount,
    buyerLockedAmount,
    purchaseFeeAmount,
    resellPurchaseFeeAmount,
  };
}

export function getOrderCollateral(
  order: OrderEntity,
  amounts: CollateralCalculation,
): number {
  return (
    (order.buyerId ? amounts.buyerLockedAmount : 0) +
    (order.sellerId ? amounts.sellerLockedAmount : 0)
  );
}

export function shouldShowSellerEarnings(
  currentUser: UserEntity | null,
  order: OrderEntity,
): boolean {
  return !!(
    currentUser?.id === order.sellerId &&
    order.reseller_earnings_for_seller &&
    order.reseller_earnings_for_seller > 0
  );
}

export function isSecondaryMarketPurchaseEligible(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return !!(
    order.status === OrderStatus.PAID &&
    order.secondaryMarketPrice &&
    order.secondaryMarketPrice > 0 &&
    currentUser?.id !== order.buyerId &&
    currentUser?.id !== order.sellerId
  );
}

export function isUserOrderParticipant(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return !!(
    currentUser?.id &&
    (currentUser.id === order.buyerId || currentUser.id === order.sellerId)
  );
}

export function isUserOrderBuyer(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return currentUser?.id === order.buyerId;
}

export function isUserOrderSeller(
  order: OrderEntity,
  currentUser: UserEntity | null,
): boolean {
  return currentUser?.id === order.sellerId;
}

export function calculateBalanceValidation({
  order,
  currentUser,
  appConfig,
}: {
  order: OrderEntity;
  currentUser: UserEntity | null;
  appConfig: AppConfigEntity | null;
}): BalanceValidationResult {
  if (!currentUser?.balance || !appConfig) {
    return { hasSufficientBalance: false, requiredAmount: 0 };
  }

  const availableBalance = currentUser.balance.sum - currentUser.balance.locked;
  let requiredAmount = 0;

  // For secondary market purchases, user needs the full secondary market price
  if (isSecondaryMarketPurchaseEligible(order, currentUser)) {
    requiredAmount = order.secondaryMarketPrice!;
  }
  // For regular buy/fulfill actions, calculate collateral based on user type
  else if (order.status === OrderStatus.ACTIVE) {
    let lockPercentageBPS = 0;

    // If order has buyer and current user is not the buyer, they are fulfilling (acting as seller)
    if (order.buyerId && currentUser.id !== order.buyerId) {
      lockPercentageBPS =
        order.fees?.seller_locked_percentage ??
        appConfig.seller_lock_percentage;
    }
    // If order has seller and current user is not the seller, they are buying (acting as buyer)
    else if (order.sellerId && currentUser.id !== order.sellerId) {
      lockPercentageBPS =
        order.fees?.buyer_locked_percentage ?? appConfig.buyer_lock_percentage;
    }

    const lockPercentage = lockPercentageBPS / 10000;
    requiredAmount = order.price * lockPercentage;
  }

  return {
    hasSufficientBalance: availableBalance >= requiredAmount,
    requiredAmount,
  };
}

// Utility functions from order-utils.ts

export function getEffectiveLockPeriod(
  collection?: CollectionEntity | null,
  appConfig?: AppConfigEntity | null,
): number {
  if (collection?.lock_period && collection.lock_period > 0) {
    return collection.lock_period;
  }
  if (appConfig?.lock_period && appConfig.lock_period > 0) {
    return appConfig.lock_period;
  }
  return FREEZE_PERIOD_DAYS; // 21 days fallback
}

export function calculateFreezeStatus(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null = null,
): boolean {
  if (!collection?.launchedAt) return false;

  const now = new Date();
  const launchedAt = firebaseTimestampToDate(collection.launchedAt);
  const lockPeriodDays = getEffectiveLockPeriod(collection, appConfig);
  const lockPeriodMs = lockPeriodDays * 24 * 60 * 60 * 1000;
  const freezeEndDate = new Date(launchedAt.getTime() + lockPeriodMs);

  return now < freezeEndDate;
}

export function calculateFreezeEndDate(
  collection: CollectionEntity | null,
  appConfig: AppConfigEntity | null = null,
): Date | null {
  if (!collection?.launchedAt) return null;

  const launchedAt = firebaseTimestampToDate(collection.launchedAt);
  const lockPeriodDays = getEffectiveLockPeriod(collection, appConfig);
  const lockPeriodMs = lockPeriodDays * 24 * 60 * 60 * 1000;
  return new Date(launchedAt.getTime() + lockPeriodMs);
}

export function formatFreezeEndDate(freezeEndDate: Date): string {
  return freezeEndDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function calculateDeadlineInfo(
  deadline: Timestamp | Date | string,
): DeadlineInfo {
  const now = new Date();
  const deadlineDate = firebaseTimestampToDate(deadline);
  const timeDiff = deadlineDate.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: true };
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

  return { days, hours, minutes, seconds, isExpired: false };
}

export function formatTimeLeft(deadlineInfo: DeadlineInfo): string {
  if (deadlineInfo.isExpired) return 'Expired';

  const { days, hours, minutes, seconds } = deadlineInfo;

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
}

export function shouldShowDeadlineTimer(order: OrderEntity): boolean {
  return Boolean(
    order.deadline &&
      (order.status === OrderStatus.PAID ||
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER),
  );
}

export function formatOrderStatus(status: OrderStatus): string {
  if (status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return 'Send to Relayer';
  }

  const formatted = status.replace(/_/g, ' ');
  return formatted.charAt(0).toUpperCase() + formatted.slice(1);
}

export function getOrderDisplayNumber(order: OrderEntity): string {
  return order.number ? `#${order.number}` : `#${order.id?.slice(-6)}`;
}

export function canCancelOrder(
  order: OrderEntity,
  currentUserId?: string,
): boolean {
  return (
    (order.status === OrderStatus.ACTIVE ||
      order.status === OrderStatus.CREATED) &&
    (currentUserId === order.buyerId || currentUserId === order.sellerId)
  );
}

export function canCreateSecondaryMarketOrder(
  order: OrderEntity,
  currentUserId?: string,
): boolean {
  return (
    order.status === OrderStatus.PAID &&
    currentUserId === order.buyerId &&
    Boolean(order.sellerId && order.buyerId)
  );
}

export function hasSecondaryMarketPrice(order: OrderEntity): boolean {
  return Boolean(order.secondaryMarketPrice && order.secondaryMarketPrice > 0);
}

export function getOtherUserId(
  order: OrderEntity,
  userRole: UserType,
): string | undefined {
  return userRole === UserType.SELLER ? order.buyerId : order.sellerId;
}

export function shouldShowUserInfo(order: OrderEntity): boolean {
  return [OrderStatus.PAID, OrderStatus.GIFT_SENT_TO_RELAYER].includes(
    order.status,
  );
}

export function shouldShowFreezeWarning(
  order: OrderEntity,
  userType: UserType,
  isFreezed: boolean,
): boolean {
  return (
    isFreezed &&
    userType === UserType.SELLER &&
    order.status === OrderStatus.PAID &&
    Boolean(order.deadline)
  );
}

export function shouldShowGiftReadySection(
  order: OrderEntity,
  userRole: UserType,
): boolean {
  return (
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
    userRole === UserType.BUYER
  );
}

export function shouldShowGiftRefundSection(
  order: OrderEntity,
  userRole: UserType,
): boolean {
  return (
    order.status === OrderStatus.CANCELLED &&
    Boolean(order.giftId) &&
    userRole === UserType.SELLER
  );
}

export const isSecondaryMarketOrder = (order: OrderEntity): boolean => {
  return (
    order.status === OrderStatus.PAID &&
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};

// Order Status Utils (moved from order-status-utils.ts)
export interface StatusConfig {
  label: string;
  className: string;
  iconColor: string;
}

export const ORDER_STATUS_CONFIG: Record<OrderStatus, StatusConfig> = {
  [OrderStatus.CREATED]: {
    label: 'Created',
    className:
      'bg-gray-500/30 text-gray-200 border-gray-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-gray-200',
  },
  [OrderStatus.ACTIVE]: {
    label: 'Active',
    className:
      'bg-blue-500/30 text-blue-200 border-blue-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-blue-200',
  },
  [OrderStatus.PAID]: {
    label: 'Paid',
    className:
      'bg-yellow-500/30 text-yellow-200 border-yellow-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-yellow-200',
  },
  [OrderStatus.GIFT_SENT_TO_RELAYER]: {
    label: 'Sent to Bot',
    className:
      'bg-purple-500/30 text-purple-200 border-purple-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-purple-200',
  },
  [OrderStatus.FULFILLED]: {
    label: 'Fulfilled',
    className:
      'bg-green-500/30 text-green-200 border-green-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-green-200',
  },
  [OrderStatus.CANCELLED]: {
    label: 'Cancelled',
    className:
      'bg-red-500/30 text-red-200 border-red-500/40 backdrop-blur-sm shadow-sm',
    iconColor: 'text-red-200',
  },
};

export function getStatusConfig(status: OrderStatus): StatusConfig {
  return ORDER_STATUS_CONFIG[status];
}

export function getStatusLabel(status: OrderStatus, intl: IntlShape): string {
  switch (status) {
    case OrderStatus.CREATED:
      return intl.formatMessage(orderStatusUtilsMessages.created);
    case OrderStatus.ACTIVE:
      return intl.formatMessage(orderStatusUtilsMessages.active);
    case OrderStatus.PAID:
      return intl.formatMessage(orderStatusUtilsMessages.paid);
    case OrderStatus.GIFT_SENT_TO_RELAYER:
      return intl.formatMessage(orderStatusUtilsMessages.giftSentToRelayer);
    case OrderStatus.FULFILLED:
      return intl.formatMessage(orderStatusUtilsMessages.fulfilled);
    case OrderStatus.CANCELLED:
      return intl.formatMessage(orderStatusUtilsMessages.cancelled);
    default:
      return ORDER_STATUS_CONFIG[status as OrderStatus]?.label || status;
  }
}

export function getDeadlineTitle(
  order: OrderEntity,
  userType: UserType,
  intl: IntlShape,
): string {
  if (order.status === OrderStatus.PAID) {
    return userType === UserType.SELLER
      ? intl.formatMessage(orderStatusUtilsMessages.timeToSendGift)
      : intl.formatMessage(orderStatusUtilsMessages.sellerDeadline);
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return userType === UserType.BUYER
      ? intl.formatMessage(orderStatusUtilsMessages.timeToClaimGift)
      : intl.formatMessage(orderStatusUtilsMessages.buyerDeadline);
  }

  return intl.formatMessage(orderStatusUtilsMessages.deadline);
}

export function getDeadlineDescription(
  order: OrderEntity,
  userType: UserType,
  intl: IntlShape,
): string {
  if (order.status === OrderStatus.PAID) {
    return userType === UserType.SELLER
      ? intl.formatMessage(
          orderStatusUtilsMessages.sendGiftToRelayerOrLoseCollateral,
        )
      : intl.formatMessage(
          orderStatusUtilsMessages.sellerMustSendGiftOrLoseCollateral,
        );
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return userType === UserType.BUYER
      ? intl.formatMessage(
          orderStatusUtilsMessages.claimGiftFromRelayerOrLoseCollateral,
        )
      : intl.formatMessage(
          orderStatusUtilsMessages.buyerMustClaimGiftOrLoseCollateral,
        );
  }

  return '';
}
