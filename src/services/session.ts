import axios from "axios";
import { BOT_TOKEN } from "../app.constants";
import { loadEnvironment } from "../config/env-loader";
import { CloudFunctionsNames, getCloudFunctionUrl } from "../firebase-service";

import { log } from "../utils/logger";
import { BotSessionEntity } from "../mikerudenko/marketplace-shared";

loadEnvironment();

export const setUserSession = async (
  userId: string,
  session: Partial<BotSessionEntity>
): Promise<void> => {
  try {
    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.saveUserSessionByBot),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
          sessionData: session,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.data.result.success) {
      throw new Error(response.data.result.message ?? "Failed to save session");
    }
  } catch (error) {
    log.error(`Failed to set session for user ${userId}`, error, {
      operation: "set_user_session",
      userId,
    });
    throw error;
  }
};

export const getUserSession = async (
  userId: string
): Promise<BotSessionEntity | undefined> => {
  try {
    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.getUserSessionByBot),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.data.result.success) {
      throw new Error(response.data.result.message ?? "Failed to get session");
    }

    return response.data.result.session ?? undefined;
  } catch (error) {
    log.error(`Failed to get session for user ${userId}`, error, {
      operation: "get_user_session",
      userId,
    });
    return undefined;
  }
};

export const clearUserSession = async (userId: string): Promise<void> => {
  try {
    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.clearUserSessionByBot),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.data.result.success) {
      throw new Error(
        response.data.result.message ?? "Failed to clear session"
      );
    }
  } catch (error) {
    log.error(`Failed to clear session for user ${userId}`, error, {
      operation: "clear_user_session",
      userId,
    });
    throw error;
  }
};

export const updateUserSession = async (
  userId: string,
  updates: Partial<BotSessionEntity>
): Promise<void> => {
  try {
    const currentSession = (await getUserSession(userId)) ?? {};
    const updatedSession = { ...currentSession, ...updates };
    await setUserSession(userId, updatedSession as BotSessionEntity);
  } catch (error) {
    log.error(`Failed to update session for user ${userId}`, error, {
      operation: "update_user_session",
      userId,
    });
    throw error;
  }
};

export const clearSessionProperty = async (
  userId: string,
  property: keyof BotSessionEntity
): Promise<void> => {
  try {
    const session = await getUserSession(userId);
    if (session) {
      delete session[property];
      if (Object.keys(session).length === 0) {
        await clearUserSession(userId);
      } else {
        await setUserSession(userId, session);
      }
    }
  } catch (error) {
    log.error(
      `Failed to clear session property ${property} for user ${userId}`,
      error,
      {
        operation: "clear_session_property",
        userId,
        property,
      }
    );
    throw error;
  }
};
