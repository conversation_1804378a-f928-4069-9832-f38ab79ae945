import { log } from "../utils/logger";

export function logHealthStatusCheckError(params: { error: unknown }) {
  log.error("Failed to check health status", params.error, {
    operation: "healthcheck_status",
  });
}

export function logBotStartTimeUpdated(params: { timestamp: string }) {
  log.healthLog("Bot start time updated", {
    status: "bot_start_time_set",
    timestamp: params.timestamp,
  });
}
