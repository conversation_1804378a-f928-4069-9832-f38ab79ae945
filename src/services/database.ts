import sqlite3 from "sqlite3";
import path from "path";

export interface UserLanguagePreference {
  tg_id: string;
  language: string;
  created_at: string;
  updated_at: string;
}

// augment-fix use lang enum here, refactor this file, DRY, write less code
export type SupportedLanguage = "en" | "uk" | "ru";

class DatabaseService {
  private db: sqlite3.Database | null = null;
  private readonly dbPath: string;

  constructor() {
    this.dbPath = path.join(process.cwd(), "data", "bot.db");
  }

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Ensure data directory exists
        const fs = require("fs");
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize database
        this.db = new sqlite3.Database(this.dbPath, (err) => {
          if (err) {
            reject(err);
            return;
          }

          // Create tables
          this.db!.run(
            `
            CREATE TABLE IF NOT EXISTS user_language_preferences (
              tg_id TEXT PRIMARY KEY,
              language TEXT NOT NULL DEFAULT 'en',
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
          `,
            (err) => {
              if (err) {
                reject(err);
                return;
              }
              console.log("Database initialized successfully");
              resolve();
            }
          );
        });
      } catch (error) {
        console.error("Failed to initialize database:", error);
        reject(error);
      }
    });
  }

  async setUserLanguage(
    tgId: string,
    language: SupportedLanguage
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      // If language is English (default), delete the record to optimize storage
      if (language === "en") {
        this.db.run(
          `DELETE FROM user_language_preferences WHERE tg_id = ?`,
          [tgId],
          (err) => {
            if (err) {
              reject(err);
              return;
            }
            resolve();
          }
        );
      } else {
        // Only store non-English language preferences
        this.db.run(
          `INSERT OR REPLACE INTO user_language_preferences (tg_id, language, updated_at)
           VALUES (?, ?, CURRENT_TIMESTAMP)`,
          [tgId, language],
          (err) => {
            if (err) {
              reject(err);
              return;
            }
            resolve();
          }
        );
      }
    });
  }

  async getUserLanguage(tgId: string): Promise<SupportedLanguage> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      this.db.get(
        `SELECT language FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        (err, row: UserLanguagePreference | undefined) => {
          if (err) {
            reject(err);
            return;
          }
          // Return stored language or default to English if no record exists
          resolve((row?.language as SupportedLanguage) || "en");
        }
      );
    });
  }

  /**
   * Check if user has an explicit language preference stored
   * Returns true only if user has a non-English preference stored
   */
  async hasExplicitLanguagePreference(tgId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      this.db.get(
        `SELECT 1 FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        (err, row: any) => {
          if (err) {
            reject(err);
            return;
          }
          // If record exists, user has explicit non-English preference
          resolve(!!row);
        }
      );
    });
  }

  async getAllUserLanguages(): Promise<UserLanguagePreference[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      this.db.all(
        `SELECT * FROM user_language_preferences ORDER BY created_at DESC`,
        (err, rows: UserLanguagePreference[]) => {
          if (err) {
            reject(err);
            return;
          }
          resolve(rows || []);
        }
      );
    });
  }

  async deleteUserLanguage(tgId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      this.db.run(
        `DELETE FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        }
      );
    });
  }

  async close(): Promise<void> {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error("Error closing database:", err);
          }
          this.db = null;
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.db) {
        resolve(false);
        return;
      }

      this.db.get("SELECT 1", (err) => {
        if (err) {
          console.error("Database health check failed:", err);
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  }

  // Get database stats
  async getStats(): Promise<{
    totalUsers: number;
    languageDistribution: Record<string, number>;
  }> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      // Get total users first
      this.db.get(
        `SELECT COUNT(*) as count FROM user_language_preferences`,
        (err, totalResult: { count: number }) => {
          if (err) {
            reject(err);
            return;
          }

          // Get language distribution
          this.db!.all(
            `SELECT language, COUNT(*) as count FROM user_language_preferences GROUP BY language`,
            (err, languageResults: { language: string; count: number }[]) => {
              if (err) {
                reject(err);
                return;
              }

              const languageDistribution: Record<string, number> = {};
              languageResults.forEach((row) => {
                languageDistribution[row.language] = row.count;
              });

              resolve({
                totalUsers: totalResult.count,
                languageDistribution,
              });
            }
          );
        }
      );
    });
  }
}

export const databaseService = new DatabaseService();
