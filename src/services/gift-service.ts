import axios from "axios";
import { log } from "../utils/logger";
import { loadEnvironment } from "../config/env-loader";
import type { GiftEntity, OrderGift } from "../miker<PERSON>nko/marketplace-shared";
import { BOT_TOKEN, FIREBASE_FUNCTIONS_BASE_URL } from "../app.constants";

loadEnvironment();

export async function getGiftById(giftId: string) {
  try {
    const response = await axios.post(
      // augment-fix move getGiftByIdByBot to enum of cloud function, do this for all simillar calls
      `${FIREBASE_FUNCTIONS_BASE_URL}/getGiftByIdByBot`,
      {
        data: {
          giftId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result as GiftEntity;
  } catch (error) {
    log.error("Error getting gift by ID", error, {
      operation: "get_gift_by_id",
      giftId,
    });
    return null;
  }
}

export async function getOrderGift(order: {
  gift?: OrderGift | null;
  giftId?: string | null;
}): Promise<OrderGift | null> {
  if (order.giftId) {
    const gift = await getGiftById(order.giftId);
    return gift ?? null;
  }

  return null;
}

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  buyer_tg_id?: string;
  seller_tg_id?: string;
  status: string;
  giftId?: string | null;
  [key: string]: any;
}

export async function getOrderOwnedGiftId(order: OrderData) {
  if (order.giftId) {
    const gift = await getOrderGift(order);
    return gift?.owned_gift_id || null;
  }

  return null;
}
