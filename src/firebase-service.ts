import axios from "axios";
import { log } from "./utils/logger";
import { loadEnvironment } from "./config/env-loader";
import { OrderEntity, OrderGift } from "./mikerudenko/marketplace-shared";
import { BOT_TOKEN, FIREBASE_FUNCTIONS_BASE_URL } from "./app.constants";

loadEnvironment();

export enum CloudFunctionsNames {
  getUserOrdersByBot = "getUserOrdersByBot",
  sendGiftToRelayerByBot = "sendGiftToRelayerByBot",
  depositGiftDirectlyByBot = "depositGiftDirectlyByBot",
  getOrderByIdByBot = "getOrderByIdByBot",
  saveUserSessionByBot = "saveUserSessionByBot",
  getUserSessionByBot = "getUserSessionByBot",
  clearUserSessionByBot = "clearUserSessionByBot",
  setGiftAsWithdrawnOnCancelledOrderByBot = "setGiftAsWithdrawnOnCancelledOrderByBot",
  getGiftByIdByBot = "getGiftByIdByBot",
  getUserGiftsAvailableForWithdrawalByBot = "getUserGiftsAvailableForWithdrawalByBot",
  withdrawGiftByBot = "withdrawGiftByBot",
}

export const getCloudFunctionUrl = (
  functionName: CloudFunctionsNames
): string => {
  return `${FIREBASE_FUNCTIONS_BASE_URL}/${functionName}`;
};

export interface GetUserOrdersResponse {
  success: boolean;
  orders: OrderEntity[]; // All orders (for backward compatibility)
  sellOrders: OrderEntity[]; // Orders where user is seller
  paidOrdersAwaitingGift: OrderEntity[]; // Paid orders waiting for gift
  createdOrdersNeedingActivation: OrderEntity[]; // Created orders for MARKET collections
  cancelledOrdersWithGifts: OrderEntity[]; // Cancelled orders with gifts for refund
  buyOrders: OrderEntity[]; // Orders where user is buyer
  count: number; // Total count
  sellOrdersCount: number; // Count of sell orders
  paidOrdersAwaitingGiftCount: number; // Count of paid orders awaiting gift
  createdOrdersNeedingActivationCount: number; // Count of created orders needing activation
  cancelledOrdersWithGiftsCount: number; // Count of cancelled orders with gifts
  buyOrdersCount: number; // Count of buy orders
  userId: string;
  message: string; // Response message
}

export interface CompletePurchaseResponse {
  success: boolean;
  message: string;
  netAmountToSeller: number;
  feeAmount: number;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export async function getUserOrdersByTgId(
  tgId: string
): Promise<GetUserOrdersResponse> {
  try {
    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.getUserOrdersByBot),
      {
        data: {
          tgId: tgId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    log.error("Error getting user orders", error, {
      operation: "get_user_orders",
      tgId,
    });
    throw new Error("Failed to get user orders");
  }
}

export function formatOrderForDisplay(order: OrderEntity): string {
  const statusEmoji = {
    created: "⚪",
    active: "🟡",
    paid: "🟠",
    gift_sent_to_relayer: "🎁",
    fulfilled: "✅",
    cancelled: "❌",
  };

  const emoji = statusEmoji[order.status] || "⚪";

  return `${emoji} Order #${order.number ?? 0}\n`;
}

export function getCompletableOrders(orders: OrderEntity[]): OrderEntity[] {
  return orders.filter((order) => order.status === "paid" && order.buyerId);
}

export function getGiftReadyOrders(orders: OrderEntity[]): OrderEntity[] {
  return orders.filter(
    (order) => order.status === "gift_sent_to_relayer" && order.buyerId
  );
}

export async function sendGiftToRelayer(
  orderId: string,
  gift: OrderGift
): Promise<any> {
  try {
    const url = getCloudFunctionUrl(CloudFunctionsNames.sendGiftToRelayerByBot);

    const response = await axios.post(
      url,
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
          gift,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    log.error("Error sending gift to relayer", error, {
      operation: "send_gift_to_relayer",
      orderId,
      gift,
    });
    throw new Error("Failed to send gift to relayer");
  }
}

export async function depositGiftDirectly(
  userTgId: string,
  gift: OrderGift,
  collectionId?: string
) {
  try {
    const url = getCloudFunctionUrl(
      CloudFunctionsNames.depositGiftDirectlyByBot
    );

    const response = await axios.post(
      url,
      {
        data: {
          userTgId,
          botToken: BOT_TOKEN,
          gift,
          collectionId,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    log.error("Error depositing gift directly", error, {
      operation: "deposit_gift_directly",
      userTgId,
      gift,
      collectionId,
    });
    throw new Error("Failed to deposit gift directly");
  }
}

export async function getOrderByIdByBot(orderId: string): Promise<any> {
  try {
    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.getOrderByIdByBot),
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    log.error("Error getting order by ID", error, {
      operation: "get_order_by_id",
      orderId,
    });
    throw new Error("Failed to get order by ID");
  }
}

export async function setGiftAsWithdrawnOnCancelledOrder(params: {
  userId: string;
  botToken: string;
  orderId: string;
}): Promise<{ success: boolean; message: string }> {
  const { userId, botToken, orderId } = params;

  try {
    log.info("Calling resetGiftOnCancelledOrder cloud function", {
      operation: "reset_gift_on_cancelled_order",
      userId,
      orderId,
    });

    const response = await axios.post(
      `${FIREBASE_FUNCTIONS_BASE_URL}/${CloudFunctionsNames.setGiftAsWithdrawnOnCancelledOrderByBot}`,
      {
        data: {
          userId,
          botToken,
          orderId,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    log.info("Reset gift on cancelled order completed", {
      operation: "reset_gift_on_cancelled_order",
      userId,
      orderId,
      success: response.data.result.success,
    });

    return response.data.result;
  } catch (error) {
    log.error("Error resetting gift on cancelled order", error, {
      operation: "reset_gift_on_cancelled_order",
      userId,
      orderId,
    });
    throw new Error("Failed to reset gift on cancelled order");
  }
}

export async function getUserGiftsAvailableForWithdrawal(
  tgId: string
): Promise<any[]> {
  if (!tgId) {
    throw new Error("Telegram ID is required");
  }

  try {
    log.info("Calling getUserGiftsAvailableForWithdrawalByBot cloud function", {
      operation: "get_user_gifts_available_for_withdrawal",
      tgId,
    });

    const response = await axios.post(
      getCloudFunctionUrl(
        CloudFunctionsNames.getUserGiftsAvailableForWithdrawalByBot
      ),
      {
        data: {
          tg_id: tgId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    log.info("Get user gifts available for withdrawal completed", {
      operation: "get_user_gifts_available_for_withdrawal",
      tgId,
      giftsCount: response.data.result?.length || 0,
    });

    return response.data.result.gifts || [];
  } catch (error) {
    log.error("Error getting user gifts available for withdrawal", error, {
      operation: "get_user_gifts_available_for_withdrawal",
      tgId,
    });

    throw new Error("Failed to get user gifts available for withdrawal");
  }
}

export async function withdrawGiftByBot(params: {
  giftId: string;
  userTgId: string;
}): Promise<{
  success: boolean;
  message?: string;
  error?: string;
  ownedGiftId?: string;
}> {
  const { giftId, userTgId } = params;

  try {
    log.info("Calling withdrawGiftByBot cloud function", {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
    });

    const response = await axios.post(
      getCloudFunctionUrl(CloudFunctionsNames.withdrawGiftByBot),
      {
        data: {
          giftId,
          userTgId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    log.info("Withdraw gift completed", {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
      success: response.data.result.success,
    });

    return response.data.result;
  } catch (error) {
    log.error("Error withdrawing gift", error, {
      operation: "withdraw_gift_by_bot",
      giftId,
      userTgId,
    });
    if (axios.isAxiosError(error) && error.response) {
      const errorMessage =
        error.response.data?.error?.message ?? "Failed to withdraw gift";
      throw new Error(errorMessage);
    }
    throw new Error("Failed to withdraw gift");
  }
}
