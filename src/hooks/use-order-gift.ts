import { useEffect, useState } from 'react';

import { getGiftById } from '@/api/gifts.api';
import type { OrderEntity, OrderGift } from '@/mikerudenko/marketplace-shared';

interface UseOrderGiftResult {
  gift: OrderGift | null;
  loading: boolean;
  error: string | null;
}

export function useOrderGift(
  order: OrderEntity | null | undefined,
): UseOrderGiftResult {
  const [gift, setGift] = useState<OrderGift | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGift = async () => {
      if (!order) {
        return;
      }

      if (order.giftId) {
        setLoading(true);
        setError(null);

        try {
          const fetchedGift = await getGiftById(order.giftId);
          setGift(fetchedGift);
          setLoading(false);
        } catch (err) {
          console.error('Error fetching gift:', err);
          setError('Failed to load gift');
          setGift(null);
          setLoading(false);
        }
      }
    };

    fetchGift();
  }, [order]);

  return { gift, loading, error };
}
