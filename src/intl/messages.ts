import { defineMessages } from "@formatjs/intl";
import { PREM_CHANNEL, PREM_SUPPORT_OFFICIAL } from "../app.constants";

// augment-fix split this file to logical subfiles with <subfile-name>.messages.ts naming convention
export const botMessages = defineMessages({
  // Common messages
  telegramIdError: {
    id: "common.telegramIdError",
    defaultMessage: "❌ Unable to identify your Telegram ID. Please try again.",
  },
  genericError: {
    id: "common.genericError",
    defaultMessage:
      "❌ Failed to process your request. Please try again later.",
  },
  welcome: {
    id: "common.welcome",
    defaultMessage: "🛍️ Welcome to the PREM Bot!",
  },
  help: {
    id: "common.help",
    defaultMessage: `Welcome to PREM! \n \n${PREM_CHANNEL} - Community \n${PREM_SUPPORT_OFFICIAL} - Support \n{relayerUsername} - Gift Relayer`,
  },

  // Button texts
  backToOrders: {
    id: "buttons.backToOrders",
    defaultMessage: "🔙 Back to Orders",
  },
  cancel: {
    id: "buttons.cancel",
    defaultMessage: "❌ Cancel",
  },
  contactSupport: {
    id: "buttons.contactSupport",
    defaultMessage: "📞 Contact Support",
  },
  depositGift: {
    id: "buttons.depositGift",
    defaultMessage: "📦 Deposit Gift",
  },
  myGifts: {
    id: "buttons.myGifts",
    defaultMessage: "🎁 My Gifts",
  },
  openMarketplace: {
    id: "buttons.openMarketplace",
    defaultMessage: "🌐 Open Marketplace",
  },
  viewAllOrders: {
    id: "buttons.viewAllOrders",
    defaultMessage: "📋 View All Orders",
  },
  viewMyOrders: {
    id: "buttons.viewMyOrders",
    defaultMessage: "👤 View My Orders",
  },

  // Business connection messages
  giftReadyForBuyer: {
    id: "businessConnection.giftReadyForBuyer",
    defaultMessage:
      "🎁 Great news! Your gift for order #{orderNumber} is ready for delivery. Please check your orders.",
  },
  giftSentError: {
    id: "businessConnection.giftSentError",
    defaultMessage: "❌ Failed to send gift to relayer. Please try again.",
  },
  giftSentSuccess: {
    id: "businessConnection.giftSentSuccess",
    defaultMessage:
      "✅ Gift successfully sent to relayer! The buyer will be notified.",
  },
  giftTransferGenericError: {
    id: "businessConnection.giftTransferGenericError",
    defaultMessage: "❌ Failed to transfer gift. Please try again.",
  },
  giftTransferredSuccess: {
    id: "businessConnection.giftTransferredSuccess",
    defaultMessage: "✅ Gift successfully transferred!",
  },
  incorrectGift: {
    id: "businessConnection.incorrectGift",
    defaultMessage:
      "❌ This gift does not match the order requirements. Please send the correct gift.",
  },
  noGiftToTransfer: {
    id: "businessConnection.noGiftToTransfer",
    defaultMessage: "❌ No gift found to transfer.",
  },
  orderNotFound: {
    id: "businessConnection.orderNotFound",
    defaultMessage:
      "❌ Order not found. Please check the order ID and try again.",
  },
  processingGift: {
    id: "businessConnection.processingGift",
    defaultMessage: "⏳ Processing your gift...",
  },
  processingWithdrawal: {
    id: "businessConnection.processingWithdrawal",
    defaultMessage: "⏳ Processing your withdrawal...",
  },
  withdrawalError: {
    id: "businessConnection.withdrawalError",
    defaultMessage: "❌ Failed to withdraw gift. Please try again.",
  },
  withdrawalSuccess: {
    id: "businessConnection.withdrawalSuccess",
    defaultMessage: "✅ Gift successfully withdrawn!",
  },

  // Callback messages
  backToMenu: {
    id: "callbacks.backToMenu",
    defaultMessage: "🏠 Back to Main Menu",
  },
  buyOrdersTitle: {
    id: "callbacks.buyOrdersTitle",
    defaultMessage: "🛒 Your Buy Orders ({count} total)",
  },
  cancelledOrdersWithGifts: {
    id: "callbacks.cancelledOrdersWithGifts",
    defaultMessage:
      "\n\n🔴 Cancelled Orders with Gifts ({count})\nThese orders were cancelled but have gifts that can be refunded.",
  },
  chooseOrderType: {
    id: "callbacks.chooseOrderType",
    defaultMessage:
      "📋 Choose Order Type\n\nSelect the type of orders you want to view:",
  },
  callbackContactSupport: {
    id: "callbacks.contactSupport",
    defaultMessage: `📞 Contact Support\n\nFor any questions or issues, please reach out to ${PREM_SUPPORT_OFFICIAL}`,
  },
  fetchBuyOrdersError: {
    id: "callbacks.fetchBuyOrdersError",
    defaultMessage: "❌ Error fetching buy orders. Please try again.",
  },
  fetchGroupOrdersError: {
    id: "callbacks.fetchGroupOrdersError",
    defaultMessage: "❌ Error fetching orders. Please try again.",
  },
  fetchSellOrdersError: {
    id: "callbacks.fetchSellOrdersError",
    defaultMessage: "❌ Error fetching sell orders. Please try again.",
  },
  callbackGenericError: {
    id: "callbacks.genericError",
    defaultMessage: "❌ An error occurred. Please try again.",
  },
  giftReadyForDelivery: {
    id: "callbacks.giftReadyForDelivery",
    defaultMessage:
      "🎁 Your gift is ready for delivery! Click the button below to receive it.",
  },
  giftReceivedSuccess: {
    id: "callbacks.giftReceivedSuccess",
    defaultMessage: "🎁 Gift received successfully! Enjoy your new item.",
  },
  group1OrderReady: {
    id: "callbacks.group1OrderReady",
    defaultMessage:
      "🟠 This order has been paid and is waiting for you to send the gift to the relayer.",
  },
  group1Title: {
    id: "callbacks.group1Title",
    defaultMessage: "🟠 Paid Orders Awaiting Gift ({count} total)",
  },
  group2OrderReady: {
    id: "callbacks.group2OrderReady",
    defaultMessage:
      "🔵 This order needs a gift to be activated. Please deposit a gift first.",
  },
  group2Title: {
    id: "callbacks.group2Title",
    defaultMessage: "🔵 Created Orders Needing Activation ({count} total)",
  },
  group3OrderReady: {
    id: "callbacks.group3OrderReady",
    defaultMessage:
      "🔴 This order was cancelled but has a gift that can be refunded.",
  },
  group3Title: {
    id: "callbacks.group3Title",
    defaultMessage: "🔴 Cancelled Orders with Gifts ({count} total)",
  },
  noActionableSellOrders: {
    id: "callbacks.noActionableSellOrders",
    defaultMessage: "📭 No actionable sell orders found.",
  },
  noBuyOrders: {
    id: "callbacks.noBuyOrders",
    defaultMessage:
      "📭 No Buy Orders\n\nYou don't have any buy orders at the moment.",
  },
  noGroup1Orders: {
    id: "callbacks.noGroup1Orders",
    defaultMessage: "📭 No paid orders awaiting gifts found.",
  },
  noGroup2Orders: {
    id: "callbacks.noGroup2Orders",
    defaultMessage: "📭 No created orders needing activation found.",
  },
  noGroup3Orders: {
    id: "callbacks.noGroup3Orders",
    defaultMessage: "📭 No cancelled orders with gifts found.",
  },
  noSellOrders: {
    id: "callbacks.noSellOrders",
    defaultMessage:
      "📭 No Sell Orders\n\nYou don't have any sell orders at the moment.",
  },
  openingMarketplace: {
    id: "callbacks.openingMarketplace",
    defaultMessage: "🌐 Opening Marketplace...",
  },
  orderHelp: {
    id: "callbacks.orderHelp",
    defaultMessage:
      "❓ Order Help\n\nIf you need assistance with your orders, please contact our support team.",
  },
  ordersNeedActivation: {
    id: "callbacks.ordersNeedActivation",
    defaultMessage:
      "\n\n🔵 Orders Need Activation ({count})\nThese orders need a gift to be activated.",
  },
  sellOrdersTitle: {
    id: "callbacks.sellOrdersTitle",
    defaultMessage: "💰 Your Sell Orders",
  },
  showOrderOptionsError: {
    id: "callbacks.showOrderOptionsError",
    defaultMessage: "❌ Error showing order options. Please try again.",
  },

  // Gift messages
  fetchingGifts: {
    id: "gifts.fetchingGifts",
    defaultMessage: "🔄 Fetching your gifts...",
  },
  noGiftsAvailable: {
    id: "gifts.noGiftsAvailable",
    defaultMessage:
      "📭 No gifts available. Send gifts to {relayerUsername} to get started!",
  },

  // Order messages
  ordersBuyOrdersTitle: {
    id: "orders.buyOrdersTitle",
    defaultMessage: "🛒 Your Buy Orders ({count} total)",
  },
  fetchingBuy: {
    id: "orders.fetchingBuy",
    defaultMessage: "🔄 Fetching your buy orders...",
  },
  fetchingSell: {
    id: "orders.fetchingSell",
    defaultMessage: "🔄 Fetching your sell orders...",
  },
  giftsReadyForDelivery: {
    id: "orders.giftsReadyForDelivery",
    defaultMessage:
      "\n\n🎁 Gifts Ready for Delivery ({count})\nThese gifts are ready to be delivered to you.",
  },
  noBuyOrdersMessage: {
    id: "orders.noBuyOrders",
    defaultMessage: "📭 You have no buy orders yet.",
  },
  noSellOrdersMessage: {
    id: "orders.noSellOrders",
    defaultMessage: "📭 You have no sell orders yet.",
  },
  ordersReadyForCompletion: {
    id: "orders.ordersReadyForCompletion",
    defaultMessage:
      "\n\n🟠 Orders Ready for Completion ({count})\nThese orders have been paid and are waiting for you to send the gift to the relayer.",
  },
  ordersSellOrdersTitle: {
    id: "orders.sellOrdersTitle",
    defaultMessage: "💰 Your Sell Orders ({count} total)",
  },

  // Support messages
  supportContactInfo: {
    id: "support.contactInfo",
    defaultMessage: `📞 For support, please contact ${PREM_SUPPORT_OFFICIAL}`,
  },

  // Additional order messages
  showingLimitedOrders: {
    id: "orders.showingLimitedOrders",
    defaultMessage: "📝 Showing first 10 orders only",
  },
  fetchErrorSell: {
    id: "orders.fetchErrorSell",
    defaultMessage: "❌ Error fetching sell orders. Please try again.",
  },

  // Order status messages
  orderStatusActive: {
    id: "callbacks.orderStatus.active",
    defaultMessage: "✅ This order is active and ready for completion.",
  },
  orderStatusPaid: {
    id: "callbacks.orderStatus.paid",
    defaultMessage:
      "💰 This order has been paid and is waiting for gift delivery.",
  },
  orderStatusCreated: {
    id: "callbacks.orderStatus.created",
    defaultMessage: "📝 This order has been created and needs activation.",
  },
  orderStatusCancelled: {
    id: "callbacks.orderStatus.cancelled",
    defaultMessage: "❌ This order has been cancelled.",
  },
  orderStatusGiftSentToRelayer: {
    id: "callbacks.orderStatus.gift_sent_to_relayer",
    defaultMessage: "🎁 Gift has been sent to relayer for delivery.",
  },

  // Missing button texts
  orderHelpButton: {
    id: "buttons.orderHelpButton",
    defaultMessage: "📋 Order Help",
  },
  readyToSendGift: {
    id: "buttons.readyToSendGift",
    defaultMessage: "🎁 I'm ready to send gift",
  },
  buyOrders: {
    id: "buttons.buyOrders",
    defaultMessage: "🛒 Buy Orders",
  },
  sellOrders: {
    id: "buttons.sellOrders",
    defaultMessage: "💰 Sell Orders",
  },
  openMarketplaceButton: {
    id: "buttons.openMarketplaceButton",
    defaultMessage: "🌐 Open Marketplace",
  },

  // Error messages
  botGenericError: {
    id: "common.botGenericError",
    defaultMessage: "Sorry, something went wrong. Please try again later.",
  },

  // Command descriptions
  startCommandDescription: {
    id: "commands.start.description",
    defaultMessage: "Start the bot and show main menu",
  },
  helpCommandDescription: {
    id: "commands.help.description",
    defaultMessage: "Show help information",
  },
  healthCommandDescription: {
    id: "commands.health.description",
    defaultMessage: "Check bot health status",
  },

  // Simulation mode messages
  simulationOrderActivation: {
    id: "simulation.orderActivation",
    defaultMessage:
      "\n\n🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.",
  },
  simulationOrderView: {
    id: "simulation.orderView",
    defaultMessage: "🔧 DEV MODE: This order is in simulation mode.",
  },
  simulationGiftDepositSuccess: {
    id: "simulation.giftDepositSuccess",
    defaultMessage:
      "🎁 Gift Details:\n• Name: {giftName}\n• Model: {modelName}\n• Symbol: {symbolName}\n• Backdrop: {backdropName}\n\nYour gift is now available in the Pram app under 'My Gifts' tab.",
  },
  simulationGiftDepositError: {
    id: "simulation.giftDepositError",
    defaultMessage:
      "❌ Failed to deposit mock gift: {errorMessage}\n\nPlease try again or contact support if the issue persists.",
  },
  simulationGiftWithdrawalSuccess: {
    id: "simulation.giftWithdrawalSuccess",
    defaultMessage:
      "✅ Gift withdrawal completed successfully in simulation mode!\n\n🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.",
  },
  simulationGiftWithdrawalError: {
    id: "simulation.giftWithdrawalError",
    defaultMessage:
      "❌ Gift withdrawal failed in simulation mode: {errorMessage}\n\nPlease try again or contact support if the issue persists.",
  },
  simulationGiftDepositMode: {
    id: "simulation.giftDepositMode",
    defaultMessage:
      "🎁 Deposit a Gift (Simulation Mode)\n\n🔧 SIMULATION MODE: Generating and depositing a mock gift...",
  },
  simulationGiftWithdrawalMode: {
    id: "simulation.giftWithdrawalMode",
    defaultMessage:
      "🎁 Gift Withdrawal (Simulation Mode)\n\n🔧 SIMULATION MODE: Processing gift withdrawal...",
  },
  simulationSellerGiftDeposit: {
    id: "simulation.sellerGiftDeposit",
    defaultMessage:
      "\n\n🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.",
  },

  // Language detection messages
  languageDetectionPromptUkrainian: {
    id: "language.detectionPromptUkrainian",
    defaultMessage:
      "🌍 We detected that you are using the app in Ukrainian language. Would you like to switch to Ukrainian?",
  },
  languageDetectionPromptRussian: {
    id: "language.detectionPromptRussian",
    defaultMessage:
      "🌍 We detected that you are using the app in Russian language. Would you like to switch to Russian?",
  },
  languageKeepEnglish: {
    id: "language.keepEnglish",
    defaultMessage: "No, I want to stay in English",
  },
  languageSwitchToUkrainian: {
    id: "language.switchToUkrainian",
    defaultMessage: "Yes, let's switch to Ukrainian",
  },
  languageSwitchToRussian: {
    id: "language.switchToRussian",
    defaultMessage: "Yes, let's switch to Russian",
  },
  languageSetToEnglish: {
    id: "language.setToEnglish",
    defaultMessage: "✅ Language set to English. Welcome to PREM Bot!",
  },
  languageSetToUkrainian: {
    id: "language.setToUkrainian",
    defaultMessage:
      "✅ Мову змінено на українську. Ласкаво просимо до PREM Bot!",
  },
  languageSetToRussian: {
    id: "language.setToRussian",
    defaultMessage: "✅ Язык изменен на русский. Добро пожаловать в PREM Bot!",
  },
});
