import { log } from "../utils/logger";

interface BaseBusinessConnectionParams {
  chat_id: string | number;
  userId: string;
}

interface BusinessConnectionWithOrderParams
  extends BaseBusinessConnectionParams {
  pendingOrderId: string;
}

interface BusinessConnectionWithGiftParams
  extends BaseBusinessConnectionParams {
  withdrawalGiftId: string;
}

export const businessConnectionLogger = {
  // Middleware lifecycle logging
  logMiddlewareStarted() {
    log.info("Business connection middleware started", {
      operation: "business_connection_middleware_start",
    });
  },

  logMiddlewareCompleted(params: BusinessConnectionWithOrderParams) {
    log.info("Business connection middleware completed", {
      operation: "business_connection_middleware_end",
      ...params,
    });
  },

  logMiddlewareError(error: unknown) {
    log.error("Error handling update", error, {
      operation: "business_connection_middleware",
    });
  },

  // Business message processing
  logNoBusinessMessage() {
    log.info("No business message found, skipping middleware", {
      operation: "business_connection_middleware",
    });
  },

  logProcessingBusinessMessage(params: { chat_id: string | number }) {
    log.info("Processing business message", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logNoUserIdFound(params: { chat_id: string | number }) {
    log.warn("No user ID found in business message", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logUserIdExtracted(params: BaseBusinessConnectionParams) {
    log.info("User ID extracted from business message", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  // Session management
  logUserSessionRetrieved(
    params: BaseBusinessConnectionParams & {
      hasSession: boolean;
      pendingOrderId?: string | undefined;
      withdrawalGiftId?: string | undefined;
    }
  ) {
    log.info("User session retrieved", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  // Gift withdrawal flow
  logGiftWithdrawalDetected(params: BusinessConnectionWithGiftParams) {
    log.info("Gift withdrawal detected", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logGiftWithdrawalSuccess(params: BusinessConnectionWithGiftParams) {
    log.info("Gift withdrawal completed successfully", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logGiftWithdrawalFailed(params: BusinessConnectionWithGiftParams) {
    log.warn("Gift withdrawal failed", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  // Direct gift deposit flow
  logDirectGiftDepositDetected(
    params: BaseBusinessConnectionParams & {
      giftToTransfer: any;
    }
  ) {
    log.info("Direct gift deposit detected (no pending order)", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logDirectGiftDepositSuccess(params: BaseBusinessConnectionParams) {
    log.info("Direct gift deposit completed successfully", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logDirectGiftDepositFailed(params: BaseBusinessConnectionParams) {
    log.warn("Direct gift deposit failed", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  // Order processing
  logNoPendingOrderFound(params: BaseBusinessConnectionParams) {
    log.warn("No pending order found for user", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logGiftExtractionResult(
    params: BusinessConnectionWithOrderParams & {
      giftIdToTransfer: any;
    }
  ) {
    log.info("Gift ID extraction result", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  logNoGiftToTransferCheck(params: BusinessConnectionWithOrderParams) {
    log.info(
      "No gift to transfer, checking for buyer gift request or cancelled gift retrieval",
      {
        operation: "business_connection_middleware",
        ...params,
      }
    );
  },

  logNoApplicableFlowFound(params: BusinessConnectionWithOrderParams) {
    log.warn("No applicable flow found for user request", {
      operation: "business_connection_middleware",
      ...params,
    });
  },

  // Debug logging (for development)
  logUpdateDebug(update: any) {
    // Only log in development to avoid cluttering production logs
    if (process.env.NODE_ENV === "development") {
      log.debug("Business connection update debug", {
        operation: "business_connection_middleware",
        update: JSON.stringify(update),
      });
    }
  },
};
