{"businessConnection.giftReadyForBuyer": "🎁 Great news! Your gift for order #{orderNumber} is ready for delivery. Please check your orders.", "businessConnection.giftSentError": "❌ Failed to send gift to relayer. Please try again.", "businessConnection.giftSentSuccess": "✅ Gift successfully sent to relayer! The buyer will be notified.", "businessConnection.giftTransferGenericError": "❌ Failed to transfer gift. Please try again.", "businessConnection.giftTransferredSuccess": "✅ Gift successfully transferred!", "businessConnection.incorrectGift": "❌ This gift does not match the order requirements. Please send the correct gift.", "businessConnection.noGiftToTransfer": "❌ No gift found to transfer.", "businessConnection.orderNotFound": "❌ Order not found. Please check the order ID and try again.", "businessConnection.processingGift": "⏳ Processing your gift...", "businessConnection.processingWithdrawal": "⏳ Processing your withdrawal...", "businessConnection.withdrawalError": "❌ Failed to withdraw gift. Please try again.", "businessConnection.withdrawalSuccess": "✅ Gift successfully withdrawn!", "buttons.backToOrders": "🔙 Back to Orders", "buttons.buyOrders": "🛒 Buy Orders", "buttons.cancel": "❌ Cancel", "buttons.contactSupport": "📞 Contact Support", "buttons.depositGift": "📦 Deposit Gift", "buttons.myGifts": "🎁 My Gifts", "buttons.openMarketplace": "🌐 Open Marketplace", "buttons.openMarketplaceButton": "🌐 Open Marketplace", "buttons.orderHelpButton": "📋 Order Help", "buttons.readyToSendGift": "🎁 I'm ready to send gift", "buttons.sellOrders": "💰 Sell Orders", "buttons.viewAllOrders": "📋 View All Orders", "buttons.viewMyOrders": "👤 View My Orders", "callbacks.backToMenu": "🏠 Back to Main Menu", "callbacks.buyOrdersTitle": "🛒 Your Buy Orders ({count} total)", "callbacks.cancelledOrdersWithGifts": "🔴 Cancelled Orders with Gifts ({count})\nThese orders were cancelled but have gifts that can be refunded.", "callbacks.chooseOrderType": "📋 Choose Order Type\n\nSelect the type of orders you want to view:", "callbacks.contactSupport": "📞 Contact Support\n\nFor any questions or issues, please reach out to @prem_support_official", "callbacks.fetchBuyOrdersError": "❌ Error fetching buy orders. Please try again.", "callbacks.fetchGroupOrdersError": "❌ Error fetching orders. Please try again.", "callbacks.fetchSellOrdersError": "❌ Error fetching sell orders. Please try again.", "callbacks.genericError": "❌ An error occurred. Please try again.", "callbacks.giftReadyForDelivery": "🎁 Your gift is ready for delivery! Click the button below to receive it.", "callbacks.giftReceivedSuccess": "🎁 Gift received successfully! Enjoy your new item.", "callbacks.group1OrderReady": "🟠 This order has been paid and is waiting for you to send the gift to the relayer.", "callbacks.group1Title": "🟠 Paid Orders Awaiting Gift ({count} total)", "callbacks.group2OrderReady": "🔵 This order needs a gift to be activated. Please deposit a gift first.", "callbacks.group2Title": "🔵 Created Orders Needing Activation ({count} total)", "callbacks.group3OrderReady": "🔴 This order was cancelled but has a gift that can be refunded.", "callbacks.group3Title": "🔴 Cancelled Orders with Gifts ({count} total)", "callbacks.noActionableSellOrders": "📭 No actionable sell orders found.", "callbacks.noBuyOrders": "📭 No Buy Orders\n\nYou don't have any buy orders at the moment.", "callbacks.noGroup1Orders": "📭 No paid orders awaiting gifts found.", "callbacks.noGroup2Orders": "📭 No created orders needing activation found.", "callbacks.noGroup3Orders": "📭 No cancelled orders with gifts found.", "callbacks.noSellOrders": "📭 No Sell Orders\n\nYou don't have any sell orders at the moment.", "callbacks.openingMarketplace": "🌐 Opening Marketplace...", "callbacks.orderHelp": "❓ Order Help\n\nIf you need assistance with your orders, please contact our support team.", "callbacks.orderStatus.active": "✅ This order is active and ready for completion.", "callbacks.orderStatus.cancelled": "❌ This order has been cancelled.", "callbacks.orderStatus.created": "📝 This order has been created and needs activation.", "callbacks.orderStatus.gift_sent_to_relayer": "🎁 Gift has been sent to relayer for delivery.", "callbacks.orderStatus.paid": "💰 This order has been paid and is waiting for gift delivery.", "callbacks.ordersNeedActivation": "🔵 Orders Need Activation ({count})\nThese orders need a gift to be activated.", "callbacks.sellOrdersTitle": "💰 Your Sell Orders", "callbacks.showOrderOptionsError": "❌ Error showing order options. Please try again.", "commands.health.description": "Check bot health status", "commands.help.description": "Show help information", "commands.start.description": "Start the bot and show main menu", "common.botGenericError": "Sorry, something went wrong. Please try again later.", "common.genericError": "❌ Failed to process your request. Please try again later.", "common.help": "Welcome to PREM! \n \n@prem_channel - Community \n@prem_support_official - Support \n{relayerUsername} - Gift Relayer", "common.telegramIdError": "❌ Unable to identify your Telegram ID. Please try again.", "common.welcome": "🛍️ Welcome to the PREM Bot!", "gifts.fetchingGifts": "🔄 Fetching your gifts...", "gifts.noGiftsAvailable": "📭 No gifts available. Send gifts to {relayerUsername} to get started!", "language.detectionPromptRussian": "🌍 We detected that you are using the app in Russian language. Would you like to switch to Russian?", "language.detectionPromptUkrainian": "🌍 We detected that you are using the app in Ukrainian language. Would you like to switch to Ukrainian?", "language.keepEnglish": "No, I want to stay in English", "language.setToEnglish": "✅ Language set to English. Welcome to PREM Bot!", "language.setToRussian": "✅ Язык изменен на русский. Добро пожаловать в PREM Bot!", "language.setToUkrainian": "✅ Мову змінено на українську. Ласкаво просимо до PREM Bot!", "language.switchToRussian": "Yes, let's switch to Russian", "language.switchToUkrainian": "Yes, let's switch to Ukrainian", "orders.buyOrdersTitle": "🛒 Your Buy Orders ({count} total)", "orders.fetchErrorSell": "❌ Error fetching sell orders. Please try again.", "orders.fetchingBuy": "🔄 Fetching your buy orders...", "orders.fetchingSell": "🔄 Fetching your sell orders...", "orders.giftsReadyForDelivery": "🎁 Gifts Ready for Delivery ({count})\nThese gifts are ready to be delivered to you.", "orders.noBuyOrders": "📭 You have no buy orders yet.", "orders.noSellOrders": "📭 You have no sell orders yet.", "orders.ordersReadyForCompletion": "🟠 Orders Ready for Completion ({count})\nThese orders have been paid and are waiting for you to send the gift to the relayer.", "orders.sellOrdersTitle": "💰 Your Sell Orders ({count} total)", "orders.showingLimitedOrders": "📝 Showing first 10 orders only", "simulation.giftDepositError": "❌ Failed to deposit mock gift: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftDepositMode": "🎁 Deposit a Gift (Simulation Mode)\n\n🔧 SIMULATION MODE: Generating and depositing a mock gift...", "simulation.giftDepositSuccess": "🎁 Gift Details:\n• Name: {giftName}\n• Model: {modelName}\n• Symbol: {symbolName}\n• Backdrop: {backdropName}\n\nYour gift is now available in the Pram app under 'My Gifts' tab.", "simulation.giftWithdrawalError": "❌ Gift withdrawal failed in simulation mode: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftWithdrawalMode": "🎁 Gift Withdrawal (Simulation Mode)\n\n🔧 SIMULATION MODE: Processing gift withdrawal...", "simulation.giftWithdrawalSuccess": "✅ Gift withdrawal completed successfully in simulation mode!\n\n🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.", "simulation.orderActivation": "🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.", "simulation.orderView": "🔧 DEV MODE: This order is in simulation mode.", "simulation.sellerGiftDeposit": "🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.", "support.contactInfo": "📞 For support, please contact @prem_support_official"}