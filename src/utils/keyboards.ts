import { Markup } from "telegraf";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { loadEnvironment } from "../config/env-loader";
import { WEB_APP_URL } from "../app.constants";
import { CallbackActions, CallbackPrefixes } from "../constants/bot-commands";

loadEnvironment();

export const createMainKeyboard = (ctx?: any) => {
  return Markup.keyboard([
    [
      Markup.button.text(T(ctx, botMessages.depositGift.id)),
      Markup.button.text(T(ctx, botMessages.myGifts.id)),
    ],
    [Markup.button.text(T(ctx, botMessages.contactSupport.id))],
  ]).resize();
};

export const createMarketplaceInlineKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
  ]);
};

export const createOrderHelpKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
    [
      Markup.button.callback(
        T(ctx, botMessages.orderHelpButton.id),
        CallbackActions.ORDER_HELP
      ),
    ],
  ]);
};

export const createSupportKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
    [
      Markup.button.callback(
        T(ctx, botMessages.contactSupport.id),
        CallbackActions.CONTACT_SUPPORT
      ),
    ],
  ]);
};

export const createOrderActionsKeyboard = (orderId: string, ctx?: any) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        T(ctx, botMessages.readyToSendGift.id),
        `${CallbackPrefixes.COMPLETE}${orderId}`
      ),
    ],
    [
      Markup.button.callback(
        T(ctx, botMessages.backToOrders.id),
        CallbackActions.BACK_TO_ORDERS
      ),
    ],
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
  ]);
};

export const createOrderBackKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        T(ctx, botMessages.backToOrders.id),
        CallbackActions.BACK_TO_ORDERS
      ),
    ],
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
  ]);
};

export const createOrderCompletionKeyboard = (orderId: string, ctx?: any) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        T(ctx, botMessages.cancel.id),
        `${CallbackPrefixes.ORDER}${orderId}`
      ),
    ],
  ]);
};

export const createOrderSuccessKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        T(ctx, botMessages.viewMyOrders.id),
        CallbackActions.BACK_TO_ORDERS
      ),
    ],
    [Markup.button.webApp(T(ctx, botMessages.openMarketplace.id), WEB_APP_URL)],
  ]);
};

export const createOrderErrorKeyboard = (ctx?: any) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        T(ctx, botMessages.viewMyOrders.id),
        CallbackActions.BACK_TO_ORDERS
      ),
    ],
    [
      Markup.button.callback(
        T(ctx, botMessages.contactSupport.id),
        CallbackActions.CONTACT_SUPPORT
      ),
    ],
  ]);
};

export { WEB_APP_URL };
