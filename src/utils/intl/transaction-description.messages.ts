import { defineMessages } from 'react-intl';

export const transactionDescriptionMessages = defineMessages({
  // Order creation - collateral locking
  lockedCollateralForBuyer: {
    id: 'transaction.description.lockedCollateralForBuyer',
    defaultMessage:
      'Locked collateral for buyer ({amount} TON, {percentage}% of {orderPrice} TON order)',
  },
  lockedCollateralForSeller: {
    id: 'transaction.description.lockedCollateralForSeller',
    defaultMessage:
      'Locked collateral for seller ({amount} TON, {percentage}% of {orderPrice} TON order)',
  },

  // Order cancellation - unlocking collateral
  collateralUnlockedDueToCancellation: {
    id: 'transaction.description.collateralUnlockedDueToCancellation',
    defaultMessage:
      'Collateral unlocked due to order cancellation ({amount} TON)',
  },
  collateralUnlockedDueToAdminCancellation: {
    id: 'transaction.description.collateralUnlockedDueToAdminCancellation',
    defaultMessage:
      'Collateral unlocked due to admin cancellation ({amount} TON)',
  },
  collateralUnlockedDueToSellerCancellation: {
    id: 'transaction.description.collateralUnlockedDueToSellerCancellation',
    defaultMessage:
      'Collateral unlocked due to seller cancellation ({amount} TON)',
  },
  collateralUnlockedDueToBuyerCancellation: {
    id: 'transaction.description.collateralUnlockedDueToBuyerCancellation',
    defaultMessage:
      'Collateral unlocked due to buyer cancellation ({amount} TON)',
  },
  unlockedBuyerCollateralForCancelledOrder: {
    id: 'transaction.description.unlockedBuyerCollateralForCancelledOrder',
    defaultMessage:
      'Unlocked buyer collateral for cancelled order #{orderNumber} ({amount} TON)',
  },

  // Cancellation penalties and compensation
  cancellationPenaltyForSeller: {
    id: 'transaction.description.cancellationPenaltyForSeller',
    defaultMessage: 'Cancellation penalty for seller ({amount} TON)',
  },
  cancellationPenaltyForBuyer: {
    id: 'transaction.description.cancellationPenaltyForBuyer',
    defaultMessage: 'Cancellation penalty for buyer ({amount} TON collateral)',
  },
  cancellationCompensationFromBuyerCollateral: {
    id: 'transaction.description.cancellationCompensationFromBuyerCollateral',
    defaultMessage:
      'Cancellation compensation from buyer collateral ({amount} TON)',
  },
  fixedCancellationFeePenalty: {
    id: 'transaction.description.fixedCancellationFeePenalty',
    defaultMessage: 'Fixed cancellation fee penalty ({amount} TON)',
  },

  // Referral fees
  referralFeeFromPurchase: {
    id: 'transaction.description.referralFeeFromPurchase',
    defaultMessage: 'Referral fee from purchase ({amount} TON)',
  },

  // Resell earnings
  resellFeeEarningsFromBuyerCancellation: {
    id: 'transaction.description.resellFeeEarningsFromBuyerCancellation',
    defaultMessage:
      'Resell fee earnings from buyer cancellation ({amount} TON)',
  },

  // Withdrawal
  withdrawalToTonWallet: {
    id: 'transaction.description.withdrawalToTonWallet',
    defaultMessage:
      'Withdrawal to TON wallet (gross: {grossAmount} TON, net: {netAmount} TON, fee: {feeAmount} TON)',
  },

  // Deposit
  depositFromTonWallet: {
    id: 'transaction.description.depositFromTonWallet',
    defaultMessage:
      'Deposit from TON wallet (original: {originalAmount} TON, after fees: {netAmount} TON)',
  },

  // Sale completion
  saleCompletedForOrder: {
    id: 'transaction.description.saleCompletedForOrder',
    defaultMessage:
      'Sale completed for order #{orderNumber} ({netAmount} TON net after fees)',
  },
});
