import { createIntl, createIntlCache } from "@formatjs/intl";

const cache = createIntlCache();

// augment-fix move locales to enum
const DEFAULT_LOCALE = "en";

export const AVAILABLE_LOCALES = ["en", "uk", "ru"] as const;
export type SupportedLocale = (typeof AVAILABLE_LOCALES)[number];

let messages: Record<SupportedLocale, Record<string, string>> = {
  en: {},
  uk: {},
  ru: {},
};

try {
  messages.en = require("../locales/en.json");
} catch {
  console.warn("Could not load English messages, using empty object");
}

try {
  messages.uk = require("../locales/uk.json");
} catch {
  console.warn("Could not load Ukrainian messages, using empty object");
}

try {
  messages.ru = require("../locales/ru.json");
} catch {
  console.warn("Could not load Russian messages, using empty object");
}

export const createIntlInstance = (
  locale: SupportedLocale = DEFAULT_LOCALE
) => {
  return createIntl(
    {
      locale,
      messages: messages[locale],
    },
    cache
  );
};

const defaultIntl = createIntlInstance();

export const T = (
  ctx: any,
  id: string,
  values: Record<string, any> = {}
): string => {
  const userLanguage = ctx?.userLanguage || DEFAULT_LOCALE;
  const intl =
    userLanguage === DEFAULT_LOCALE
      ? defaultIntl
      : createIntlInstance(userLanguage);
  const result = intl.formatMessage({ id }, values);
  return Array.isArray(result) ? result.join("") : result;
};

export const TNoContext = (
  id: string,
  values: Record<string, any> = {},
  locale: SupportedLocale = DEFAULT_LOCALE
): string => {
  const intl =
    locale === DEFAULT_LOCALE ? defaultIntl : createIntlInstance(locale);
  const result = intl.formatMessage({ id }, values);
  return Array.isArray(result) ? result.join("") : result;
};

export const formatMessage = T;

export { defaultIntl as intl };
