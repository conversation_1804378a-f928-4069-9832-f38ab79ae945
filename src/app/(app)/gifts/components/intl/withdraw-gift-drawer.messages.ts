import { defineMessages } from 'react-intl';

export const withdrawGiftDrawerMessages = defineMessages({
  withdrawGift: {
    id: 'withdrawGiftDrawer.withdrawGift',
    defaultMessage: 'Withdraw Gift',
  },
  withdrawInstructions: {
    id: 'withdrawGiftDrawer.withdrawInstructions',
    defaultMessage: 'Follow these steps to withdraw your gift',
  },
  instructionStep1: {
    id: 'withdrawGiftDrawer.instructionStep1',
    defaultMessage:
      'You need to go to the bot to click on the "My Gifts" button.',
  },
  instructionStep2: {
    id: 'withdrawGiftDrawer.instructionStep2',
    defaultMessage:
      'And you will see your gifts you can withdraw. Select specific gift you want to withdraw.',
  },
  instructionStep3: {
    id: 'withdrawGiftDrawer.instructionStep3',
    defaultMessage: 'Go to a relayer and write "Get my gift."',
  },
  openBot: {
    id: 'withdrawGiftDrawer.openBot',
    defaultMessage: 'Open Bot',
  },
  close: {
    id: 'withdrawGiftDrawer.close',
    defaultMessage: 'Close',
  },
});
