'use client';

import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { getEligibleOrdersForGiftLinking } from '@/api/gifts.api';
import { linkGiftToOrder } from '@/api/link-gift-to-order.api';
import { formatTonValue } from '@/app/(admin)/admin/fees-management/fees-management-utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import type { GiftEntity, OrderEntity } from '@/mikerudenko/marketplace-shared';
import { OrderStatus } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { linkGiftToOrderDrawerMessages } from './intl/link-gift-to-order-drawer.messages';

interface LinkGiftToOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gift: GiftEntity | null;
  onGiftLinked: () => void;
}

export function LinkGiftToOrderDrawer({
  open,
  onOpenChange,
  gift,
  onGiftLinked,
}: LinkGiftToOrderDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, collections } = useRootContext();
  const [eligibleOrders, setEligibleOrders] = useState<OrderEntity[]>([]);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [linking, setLinking] = useState(false);

  const collection = gift
    ? collections.find((c) => c.id === gift.collectionId)
    : null;

  useEffect(() => {
    if (open && gift && currentUser?.id) {
      fetchEligibleOrders();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, gift, currentUser?.id]);

  const fetchEligibleOrders = async () => {
    if (!gift || !currentUser?.id) return;

    setLoading(true);
    try {
      const orders = await getEligibleOrdersForGiftLinking(
        currentUser.id,
        gift.collectionId,
      );
      setEligibleOrders(orders);
    } catch (error) {
      console.error('Error fetching eligible orders:', error);
      toast.error('Failed to load eligible orders');
    } finally {
      setLoading(false);
    }
  };

  const handleOrderSelect = (orderId: string) => {
    setSelectedOrderId(orderId);
  };

  const handleLinkGift = async () => {
    if (!gift || !selectedOrderId) return;

    setLinking(true);
    try {
      const result = await linkGiftToOrder(gift.id!, selectedOrderId);

      if (result.success) {
        toast.success('Gift linked to order successfully!');
        onGiftLinked();
        onOpenChange(false);
        setSelectedOrderId(null);
      } else {
        toast.error(result.message || 'Failed to link gift to order');
      }
    } catch (error) {
      console.error('Error linking gift to order:', error);
      toast.error('Failed to link gift to order');
    } finally {
      setLinking(false);
    }
  };

  if (!gift) return null;

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange}>
      <DrawerHeader title={t(linkGiftToOrderDrawerMessages.linkGiftToOrder)} />

      <div className="space-y-4">
        <div className="text-center">
          <p className="text-[#708499] text-sm">
            {t(linkGiftToOrderDrawerMessages.selectOrderToLink, {
              collectionName: collection?.name || 'Unknown Collection',
            })}
          </p>
        </div>

        {loading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={`order-skeleton-${index}`}
                className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-3 animate-pulse"
              >
                <div className="h-4 bg-[#17212b] rounded mb-2" />
                <div className="h-3 bg-[#17212b] rounded w-2/3" />
              </div>
            ))}
          </div>
        ) : eligibleOrders.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-[#708499]">
              {t(linkGiftToOrderDrawerMessages.noEligibleOrders)}
            </p>
          </div>
        ) : (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {eligibleOrders.map((order) => (
              <Card
                key={order.id}
                className={`cursor-pointer transition-colors ${
                  selectedOrderId === order.id
                    ? 'bg-purple-500/20 border-purple-500'
                    : 'bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441]'
                }`}
                onClick={() => handleOrderSelect(order.id!)}
              >
                <CardContent className="p-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-white font-medium">
                        Order #{order.number}
                      </p>
                      <p className="text-[#708499] text-sm">
                        {formatTonValue(order.price)} TON
                      </p>
                    </div>
                    <div className="text-right">
                      <div
                        className={`px-2 py-1 rounded text-xs ${
                          order.status === OrderStatus.CREATED
                            ? 'bg-blue-500/20 text-blue-400'
                            : 'bg-green-500/20 text-green-400'
                        }`}
                      >
                        {order.status === OrderStatus.CREATED
                          ? 'Created'
                          : 'Paid'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
          >
            {t(linkGiftToOrderDrawerMessages.cancel)}
          </Button>
          <Button
            onClick={handleLinkGift}
            disabled={!selectedOrderId || linking}
            className="flex-1 bg-purple-500 hover:bg-purple-600 text-white disabled:opacity-50"
          >
            {linking
              ? t(linkGiftToOrderDrawerMessages.linking)
              : t(linkGiftToOrderDrawerMessages.linkGiftToOrder)}
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
