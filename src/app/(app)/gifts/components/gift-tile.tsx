'use client';

import { Info, <PERSON>, Minus, Plus } from 'lucide-react';
import { useIntl } from 'react-intl';

import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { GiftEntity, OrderEntity } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { giftsPageMessages } from '../intl/gifts-page.messages';

interface GiftTileProps {
  gift: GiftEntity;
  relatedOrder?: OrderEntity;
  onOrderInfoClick?: (order: OrderEntity) => void;
  onSellGiftClick?: (gift: GiftEntity) => void;
  onCreateSellOrderClick?: (gift: GiftEntity) => void;
  onWithdrawGiftClick?: (gift: GiftEntity) => void;
}

export function GiftTile({
  gift,
  relatedOrder,
  onOrderInfoClick,
  onSellGiftClick,
  onCreateSellOrderClick,
  onWithdrawGiftClick,
}: GiftTileProps) {
  const { formatMessage: t } = useIntl();
  const { collections } = useRootContext();

  const collection = collections.find((c) => c.id === gift.collectionId);

  const handleOrderInfoClick = () => {
    if (relatedOrder && onOrderInfoClick) {
      onOrderInfoClick(relatedOrder);
    }
  };

  const handleSellGiftClick = () => {
    if (onSellGiftClick) {
      onSellGiftClick(gift);
    }
  };

  const handleWithdrawGiftClick = () => {
    if (onWithdrawGiftClick) {
      onWithdrawGiftClick(gift);
    }
  };

  return (
    <Card className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group">
      <CardContent className="p-2 flex flex-col h-full">
        <div className="relative mb-1">
          <div className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]">
            <TgsOrImageGift
              isImage={true}
              gift={gift}
              className="w-full h-full"
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </div>

        <div className="flex items-center justify-between mt-2 mb-2">
          <div className="truncate">
            <p className="text-sm font-medium text-white">
              {collection?.name || 'Unknown Collection'}
            </p>
          </div>
        </div>

        <div className="mt-auto">
          {relatedOrder ? (
            <Button
              variant="outline"
              size="sm"
              className="w-full bg-[#232e3c] border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#2a3441] hover:border-[#4a5a6c] transition-colors duration-200 cursor-pointer"
              onClick={handleOrderInfoClick}
            >
              <Info className="w-4 h-4" />
              {t(giftsPageMessages.orderInfo)}
            </Button>
          ) : (
            <div className="space-y-2">
              <Button
                variant="default"
                size="sm"
                className="w-full bg-[#34c759] hover:bg-[#28a745] text-white border-0 shadow-lg transition-colors duration-200 cursor-pointer"
                onClick={handleSellGiftClick}
              >
                <Link className="w-4 h-4" />
                {t(giftsPageMessages.attachToOrder)}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full bg-[#6ab2f2]! hover:bg-[#5a9fd9]! text-white border-0 shadow-lg transition-colors duration-200 cursor-pointer"
                onClick={() => onCreateSellOrderClick?.(gift)}
              >
                <Plus className="w-4 h-4" />
                {t(giftsPageMessages.createSellOrder)}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full bg-[#ec3942]! hover:bg-[#ec3942]/90! text-white border-0 shadow-lg transition-colors duration-200 cursor-pointer"
                onClick={handleWithdrawGiftClick}
              >
                <Minus className="w-4 h-4" />
                {t(giftsPageMessages.withdrawGift)}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
