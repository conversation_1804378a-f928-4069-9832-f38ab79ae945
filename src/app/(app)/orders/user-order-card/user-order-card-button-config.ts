import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { OrderStatus, UserType } from '@/mikerudenko/marketplace-shared';

import { userOrderCardMessages } from './intl/user-order-card.messages';

export interface ButtonConfig {
  show: boolean;
  handler?: () => void;
  className: string;
  message: any;
  dataAttr: string;
}

export interface UserOrderCardButtonProps {
  order: OrderEntity;
  userType: UserType;
  hasGift: boolean;
  onSendAGiftClick?: () => void;
  onGetAGiftClick?: () => void;
  onResellOrder?: () => void;
  onActivateOrder?: () => void;
  onGetCancelledGift?: () => void;
  onAttachGiftClick?: () => void;
}

export function createButtonConfig({
  order,
  userType,
  hasGift,
  onSendAGiftClick,
  onGetAGiftClick,
  onResellOrder,
  onActivateOrder,
  onGetCancelledGift,
  onAttachGiftClick,
}: UserOrderCardButtonProps): Record<string, ButtonConfig> {
  return {
    sendGift: {
      show: !!(
        order.deadline &&
        onSendAGiftClick &&
        userType === UserType.SELLER &&
        order.status === OrderStatus.PAID
      ),
      handler: onSendAGiftClick,
      className: 'bg-purple-500 hover:bg-purple-600',
      message: userOrderCardMessages.sendAGift,
      dataAttr: 'data-send-gift-button',
    },
    getGift: {
      show: !!(
        onGetAGiftClick &&
        userType === UserType.BUYER &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER
      ),
      handler: onGetAGiftClick,
      className: 'bg-purple-500 hover:bg-purple-600',
      message: userOrderCardMessages.getAGift,
      dataAttr: 'data-get-gift-button',
    },
    resell: {
      show: !!(
        userType === UserType.BUYER &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
        onResellOrder
      ),
      handler: onResellOrder,
      className: 'bg-[#6ab2f2] hover:bg-[#6ab2f2]/90',
      message: userOrderCardMessages.resellThisOrder,
      dataAttr: 'data-resell-button',
    },
    activate: {
      show: !!(
        userType === UserType.SELLER &&
        order.status === OrderStatus.CREATED &&
        onActivateOrder
      ),
      handler: onActivateOrder,
      className: 'bg-orange-500 hover:bg-orange-600',
      message: userOrderCardMessages.activateOrder,
      dataAttr: 'data-activate-order-button',
    },
    getCancelled: {
      show: !!(
        userType === UserType.SELLER &&
        order.status === OrderStatus.CANCELLED &&
        hasGift &&
        onGetCancelledGift
      ),
      handler: onGetCancelledGift,
      className: 'bg-red-500 hover:bg-red-600',
      message: userOrderCardMessages.getCancelledGift,
      dataAttr: 'data-get-cancelled-gift-button',
    },
    attachGift: {
      show: !!(
        userType === UserType.SELLER &&
        ((order.status === OrderStatus.PAID && order.deadline) ||
          order.status === OrderStatus.CREATED) &&
        !hasGift &&
        onAttachGiftClick
      ),
      handler: onAttachGiftClick,
      className: 'bg-green-500 hover:bg-green-600',
      message: userOrderCardMessages.attachGift,
      dataAttr: 'data-attach-gift-button',
    },
  };
}
