'use client';

import { OrderImage } from '@/components/shared/order-image';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { Card, CardContent } from '@/components/ui/card';
import { OrderDeadlineTimer } from '@/components/ui/order/order-deadline-timer';
import { OrderFreezeWarning } from '@/components/ui/order/order-freeze-warning';
import { useOrderTimers } from '@/hooks/use-order-timers';
import type { OrderEntity, UserType } from '@/mikerudenko/marketplace-shared';
import { OrderStatus } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import { isSecondaryMarketOrder } from '@/services/order-service';

import { ActionButton } from './user-order-card-action-button';
import { createButtonConfig } from './user-order-card-button-config';
import { UserOrderCardHeader } from './user-order-card-header';
import { UserOrderCardInfo } from './user-order-card-info';

interface UserOrderCardProps {
  order: OrderEntity;
  userType: UserType;
  onClick: () => void;
  onSendAGiftClick?: () => void;
  onGetAGiftClick?: () => void;
  onResellOrder?: () => void;
  onActivateOrder?: () => void;
  onGetCancelledGift?: () => void;
  onAttachGiftClick?: () => void;
}

export function UserOrderCard({
  order,
  userType,
  onClick,
  onSendAGiftClick,
  onGetAGiftClick,
  onResellOrder,
  onActivateOrder,
  onGetCancelledGift,
  onAttachGiftClick,
}: UserOrderCardProps) {
  const { collections } = useRootContext();
  const collection =
    collections.find((c) => c.id === order.collectionId) || null;
  const { isFreezed } = useOrderTimers({ order, collection });

  const showDeadlineTimer =
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  const showFreezeWarning =
    order.status === OrderStatus.PAID && isFreezed && userType === 'seller';

  const isSecondary = isSecondaryMarketOrder(order);

  const buttonConfig = createButtonConfig({
    order,
    userType,
    hasGift: !!order.giftId,
    onSendAGiftClick,
    onGetAGiftClick,
    onResellOrder,
    onActivateOrder,
    onGetCancelledGift,
    onAttachGiftClick,
  });

  const buttonDataAttrs = Object.values(buttonConfig).map(
    (config) => config.dataAttr,
  );

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    const isButtonClick = buttonDataAttrs.some((attr) =>
      target.closest(`[${attr}]`),
    );

    if (isButtonClick) {
      return;
    }
    onClick();
  };

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={handleCardClick}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="relative mb-1">
          <OrderImage
            order={order}
            collection={collection}
            className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]"
          >
            <UserOrderCardHeader order={order} />
            {isSecondary && (
              <SecondaryMarketBadge className="absolute top-1.5 right-1.5" />
            )}
          </OrderImage>
        </div>

        <UserOrderCardInfo order={order} collection={collection} />

        {showDeadlineTimer && (
          <OrderDeadlineTimer
            {...{
              order,
              collection,
              userType,
            }}
            className={showFreezeWarning ? 'mb-1' : ''}
          />
        )}

        {showFreezeWarning && (
          <OrderFreezeWarning
            order={order}
            userType={userType}
            collection={collection}
            isFreezed={isFreezed}
          />
        )}

        <ActionButton config={buttonConfig.sendGift} />
        <ActionButton config={buttonConfig.getGift} />
        <ActionButton config={buttonConfig.resell} />
        <ActionButton config={buttonConfig.activate} />
        <ActionButton config={buttonConfig.attachGift} />
        <ActionButton config={buttonConfig.getCancelled} />
      </CardContent>
    </Card>
  );
}
