'use client';

import { But<PERSON> } from '@telegram-apps/telegram-ui';
import { Handshake, Plus } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { AuthWrapper } from '@/components/auth/auth-wrapper';
import { OrderDetailsDrawer } from '@/components/order-details/order-details-drawer/order-details-drawer';
import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { Tabs, TabsContent } from '@/components/ui/tabs';
// import { useAppCache } from '@/contexts/AppCacheContext';
import { useOrderListFilters } from '@/contexts/OrderListFiltersContext';
import {
  // CachePatterns,
  SHOW_ACTIVITY_TAB,
} from '@/core.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useScrollPreservation } from '@/hooks/use-scroll-preservation';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { UserType } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { ActivityTable } from './marketplace/activity-table';
import { CreateOrderDrawer } from './marketplace/create-order-drawer';
import { MarketplaceTabs } from './marketplace/marketplace-tabs';
import { ResellOrderDrawer } from './marketplace/resell/resell-order-drawer';
import type { TabType } from './marketplace/use-marketplace-orders';
import { useMarketplaceOrders } from './marketplace/use-marketplace-orders';

export function MarketplaceContent() {
  const { currentUser } = useRootContext();
  // const cache = useAppCache();
  const [activeTab, setActiveTab] = useState<TabType>('buyers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [showResellDrawer, setShowResellDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  useScrollPreservation({ isOpen: showCreateOrderDrawer });
  useScrollPreservation({ isOpen: showOrderDetailsDrawer });
  useScrollPreservation({ isOpen: showResellDrawer });

  const { debouncedFilters, collections } = useOrderListFilters();
  const ordersFilters = useMemo(
    () => ({
      minPrice: debouncedFilters.minPrice
        ? parseFloat(debouncedFilters.minPrice)
        : undefined,
      maxPrice: debouncedFilters.maxPrice
        ? parseFloat(debouncedFilters.maxPrice)
        : undefined,
      collectionId: debouncedFilters.selectedCollection || undefined,
      sortBy: debouncedFilters.sortBy,
      currentUserId: currentUser?.id,
    }),
    [debouncedFilters, currentUser?.id],
  );

  const {
    sellersState,
    buyersState,
    activityState,
    loadOrders,
    loadMoreOrders,
  } = useMarketplaceOrders({
    filters: ordersFilters,
    activeTab,
  });

  const getCurrentState = () => {
    switch (activeTab) {
      case 'sellers':
        return sellersState;
      case 'buyers':
        return buyersState;
      case 'activity':
        return SHOW_ACTIVITY_TAB ? activityState : sellersState;
      default:
        return sellersState;
    }
  };

  const currentState = getCurrentState();

  const loadMoreRef = useInfiniteScroll({
    hasMore: currentState.hasMore,
    loading: currentState.loading,
    onLoadMore: loadMoreOrders,
  });

  useEffect(() => {
    loadOrders();
  }, [activeTab, ordersFilters, loadOrders]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    // cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    // cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    // cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders();
  };

  const handleCreateOrderSuccess = () => {
    setShowCreateOrderDrawer(false);
    handleOrderAction();
  };

  const handleResellSuccess = () => {
    setShowResellDrawer(false);
    handleOrderAction();
  };

  const handleCreateOrderClick = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleResellOrderClick = () => {
    setShowResellDrawer(true);
  };

  return (
    <div className="container mx-auto space-y-6">
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as TabType)}
      >
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="order"
            orders={sellersState.orders}
            loading={sellersState.loading}
            loadingMore={sellersState.loadingMore}
            hasMore={sellersState.hasMore}
            emptyMessage="No orders found for sellers"
            collections={collections}
            onOrderClick={handleOrderClick}
            activeTab={activeTab}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="order"
            orders={buyersState.orders}
            loading={buyersState.loading}
            loadingMore={buyersState.loadingMore}
            hasMore={buyersState.hasMore}
            emptyMessage="No orders found for buyers"
            collections={collections}
            onOrderClick={handleOrderClick}
            activeTab={activeTab}
          />
        </TabsContent>

        {SHOW_ACTIVITY_TAB && (
          <TabsContent value="activity" className="space-y-4">
            <ActivityTable
              orders={activityState.orders}
              loading={activityState.loading}
              loadingMore={activityState.loadingMore}
              collections={collections}
            />
          </TabsContent>
        )}
      </Tabs>

      <div className="fixed bottom-20 right-4 flex flex-col gap-3 z-40">
        <AuthWrapper>
          <Button
            size="s"
            className="bg-[#6ab2f2] text-white hover:bg-[#5a9fd9] [&>h6]:flex [&>h6]:items-center [&>h6]:gap-2 rounded-full w-14 h-14 overflow-hidden"
            onClick={handleCreateOrderClick}
          >
            <Plus className="h-6 w-6" />
          </Button>
        </AuthWrapper>

        <AuthWrapper>
          <Button
            size="s"
            className="overflow-hidden bg-[#6ab2f2] text-white hover:bg-[#5a9fd9] [&>h6]:flex [&>h6]:items-center [&>h6]:gap-2 rounded-full w-14 h-14"
            onClick={handleResellOrderClick}
          >
            <Handshake className="h-6 w-6" />
          </Button>
        </AuthWrapper>
      </div>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        userType={activeTab === 'buyers' ? UserType.BUYER : UserType.SELLER}
        onOrderCreated={handleCreateOrderSuccess}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={activeTab === 'buyers' ? UserType.SELLER : UserType.BUYER}
        onOrderAction={handleOrderAction}
      />

      <ResellOrderDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        collections={collections}
        onOrderResold={handleResellSuccess}
      />
    </div>
  );
}
