import { log } from "../utils/logger";

export function logUserBuyOrdersError(params: {
  error: unknown;
  userId: string;
  chatId: string;
}) {
  log.error("Error fetching user buy orders", params.error, {
    operation: "bot_operation",
    component: "handle_get_my_buy_orders_button",
    userId: params.userId,
    chatId: params.chatId,
  });
}

export function logUserSellOrdersError(params: {
  error: unknown;
  userId: string;
  chatId: string;
}) {
  log.error("Error fetching user sell orders", params.error, {
    operation: "bot_operation",
    component: "handle_get_my_sell_orders_button",
    userId: params.userId,
    chatId: params.chatId,
  });
}

export function logHealthCommandError(params: {
  error: unknown;
  chatId: string;
  userId: string;
}) {
  log.error("Error in health command", params.error, {
    operation: "health_command",
    chatId: params.chatId,
    userId: params.userId,
  });
}
