import { Context, Markup } from "telegraf";
import { PREM_RELAYER_USERNAME } from "../app.constants";
import { getUserGiftsAvailableForWithdrawal } from "../firebase-service";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { clearUserSession } from "../services/session";
import {
  getSimulationService,
  isSimulationEnabled,
} from "../services/simulation";
import { createMarketplaceInlineKeyboard } from "../utils/keyboards";
import { log } from "../utils/logger";

// augment-fix no intl in many places in this file

export const handleGetMyGiftsButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(T(ctx, botMessages.telegramIdError.id));
      return;
    }

    ctx.reply(T(ctx, botMessages.fetchingGifts.id));

    const gifts = await getUserGiftsAvailableForWithdrawal(tgId);

    if (!gifts || gifts.length === 0) {
      ctx.reply(
        T(ctx, botMessages.noGiftsAvailable.id, {
          relayerUsername: PREM_RELAYER_USERNAME,
        }),
        createMarketplaceInlineKeyboard(ctx)
      );
      return;
    }

    let message = `🎁 Your Gifts Available for Withdrawal (${gifts.length})\n\n`;

    gifts.forEach((gift, index) => {
      const baseName = gift.base_name || "Unknown Gift";
      const modelName = gift.model?.name ? `(${gift.model.name})` : "";
      const giftName = `${baseName} ${modelName}`.trim();
      const orderInfo = gift.relatedOrder
        ? `\n📦 Order #${gift.relatedOrder.number} (${gift.relatedOrder.status})`
        : "\n📦 No linked order";

      message += `${index + 1}. ${giftName}${orderInfo}\n\n`;
    });

    message +=
      "Use the buttons below to withdraw specific gifts or manage your orders.";

    // Create buttons for gift withdrawal (limit to first 10 gifts)
    const giftButtons = gifts
      .slice(0, 10)
      .map((gift) => [
        Markup.button.callback(
          `Withdraw ${gift.base_name || "Gift"} ${
            gift.model?.name ? `(${gift.model.name})` : ""
          } ${
            gift.relatedOrder ? `(Order #${gift.relatedOrder.number})` : ""
          }`.trim(),
          `withdraw_gift_${gift.id}`
        ),
      ]);

    giftButtons.push([
      Markup.button.callback("🏪 Open Marketplace", "open_marketplace"),
    ]);

    if (gifts.length > 10) {
      message += `\n\n⚠️ Showing first 10 gifts. You have ${
        gifts.length - 10
      } more gifts available.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(giftButtons));
  } catch (error) {
    log.error("Error in handleGetMyGiftsButton", {
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      "❌ Error fetching your gifts. Please try again later.",
      createMarketplaceInlineKeyboard(ctx)
    );
  }
};

export const handleContactSupportButton = (ctx: Context) => {
  ctx.reply(
    T(ctx, botMessages.supportContactInfo.id),
    createMarketplaceInlineKeyboard(ctx)
  );
};

export const handleDepositGiftButton = async (ctx: Context) => {
  const tgId = ctx.from?.id?.toString();

  if (!tgId) {
    ctx.reply(T(ctx, botMessages.telegramIdError.id));
    return;
  }

  // Clear user session as it's no longer relevant after deposit operation
  try {
    await clearUserSession(tgId);
  } catch (error) {
    log.warn("Failed to clear user session on deposit gift", {
      operation: "deposit_gift_clear_session",
      tgId,
      error: error instanceof Error ? error.message : String(error),
    });
    // Continue with deposit operation even if session clear fails
  }

  if (isSimulationEnabled()) {
    const simulationService = getSimulationService();
    await simulationService.handleGiftDepositSimulation({
      ctx,
      tgId,
    });
  } else {
    // Normal mode - show instructions
    const message = `🎁 Deposit a Gift

You should go to ${PREM_RELAYER_USERNAME} and just deposit a gift. Then, you will see your deposited gift in the Pram app in the tab called 'My Gifts.'

Steps:
1. Go to ${PREM_RELAYER_USERNAME}
2. Send your gift to the relayer
3. Your gift will appear in the Pram app under 'My Gifts'

This allows you to deposit gifts without linking them to a specific order.`;

    ctx.reply(message, createMarketplaceInlineKeyboard(ctx));
  }
};
