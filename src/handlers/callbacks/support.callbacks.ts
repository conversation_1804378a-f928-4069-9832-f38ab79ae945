import { Context } from "telegraf";
import {
  createSupportKeyboard,
  createMarketplaceInlineKeyboard,
} from "../../utils/keyboards";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";

export const handleOrderHelpCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(T(ctx, botMessages.orderHelp.id), createSupportKeyboard(ctx));
};

export const handleContactSupportCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    T(ctx, botMessages.callbackContactSupport.id),
    createMarketplaceInlineKeyboard(ctx)
  );
};
