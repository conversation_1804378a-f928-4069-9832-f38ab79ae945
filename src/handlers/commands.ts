import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { PREM_RELAYER_USERNAME } from "../app.constants";
import { HealthcheckService } from "../services/healthcheck";
import { logHealthCommandError } from "./handlers.logger";

export const handleStartCommand = (ctx: Context) => {
  ctx.reply(T(ctx, botMessages.welcome.id), createMainKeyboard(ctx));
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(
    T(ctx, botMessages.help.id, {
      relayerUsername: PREM_RELAYER_USERNAME,
    }),
    createMarketplaceInlineKeyboard(ctx)
  );
};

export const handleHealthCommand = async (ctx: Context) => {
  try {
    const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
    const isHealthy = await HealthcheckService.isHealthy();

    if (lastHealthcheck) {
      // augment-fix no intl
      const healthStatus = isHealthy ? "✅ Healthy" : "⚠️ Unhealthy";
      const message = `${healthStatus}\n\nLast healthcheck: ${lastHealthcheck}`;
      ctx.reply(message);
    } else {
      // augment-fix no intl
      ctx.reply("❌ No healthcheck data found");
    }
  } catch (error) {
    logHealthCommandError({
      error,
      chatId: String(ctx.chat?.id),
      userId: String(ctx.from?.id),
    });
    // augment-fix no intl
    ctx.reply("❌ Error checking health status");
  }
};
