import { log } from "./utils/logger";

export function logStartupConfiguration(params: {
  NODE_ENV: string;
  PORT: number;
  WEBHOOK_URL?: string;
  processId: number;
}) {
  log.info("Startup Configuration", {
    operation: "bot_startup",
    NODE_ENV: params.NODE_ENV,
    PORT: params.PORT,
    WEBHOOK_URL: params.WEBHOOK_URL
      ? params.WEBHOOK_URL.substring(0, 50) + "..."
      : "Not set",
    processId: params.processId,
  });
}

export function logBotLaunchFailed(params: { error: any }) {
  log.error("Failed to launch bot", params.error, {
    operation: "bot_startup",
    mode: "polling",
  });
}

export function logBotConflictWarning() {
  log.warn("Another bot instance might be running. Please stop it first.", {
    operation: "bot_startup",
    mode: "polling",
    error_type: "conflict",
  });
}

export function logMenuButtonSetFailed(params: {
  error: unknown;
  webAppUrl: string;
}) {
  log.error("Failed to set menu button", params.error, {
    operation: "bot_configuration",
    component: "menu_button",
    webAppUrl: params.webAppUrl,
  });
}

export function logCommandsSetFailed(params: { error: unknown }) {
  log.warn("Failed to set commands", {
    operation: "bot_configuration",
    component: "commands",
    error: params.error,
  });
}

export function logBotDescriptionSetFailed(params: { error: unknown }) {
  log.error("Failed to set bot description", params.error, {
    operation: "bot_configuration",
    component: "description",
  });
}

export function logBotStartFailed(params: { error: unknown }) {
  log.error("Failed to start bot", params.error, {
    operation: "bot_startup",
  });
}

export function logGracefulShutdownStarted(params: { signal: string }) {
  log.info(`Received ${params.signal}, shutting down gracefully`, {
    operation: "bot_shutdown",
    signal: params.signal,
  });
}

export function logGracefulShutdownTimeout(params: { signal: string }) {
  log.error("Graceful shutdown timeout, forcing exit", undefined, {
    operation: "bot_shutdown",
    signal: params.signal,
  });
}

export function logHttpServerStopping() {
  log.info("Stopping HTTP server", {
    operation: "bot_shutdown",
    step: "http_server",
  });
}

export function logWebhookCleanupSkipped() {
  log.info("Skipping webhook cleanup for faster shutdown", {
    operation: "bot_shutdown",
    step: "webhook_cleanup_skip",
  });
}

export function logGracefulShutdownCompleted() {
  log.info("Graceful shutdown completed", {
    operation: "bot_shutdown",
    status: "completed",
  });
}

export function logShutdownError(params: { error: unknown }) {
  log.error("Error during shutdown", params.error, {
    operation: "bot_shutdown",
    status: "error",
  });
}

export function logBotStarting() {
  log.botLog("Starting Marketplace Bot", {
    operation: "bot_startup",
  });
}

export function logMenuButtonConfigured() {
  log.botLog("Menu button configured successfully", {
    operation: "bot_configuration",
    component: "menu_button",
    status: "success",
    menuButtonText: "PREM",
    webAppUrl: process.env.WEB_APP_URL?.substring(0, 50) + "...",
  });
}

export function logMenuButtonVerification(params: {
  menuButtonType: string;
  menuButtonText?: string;
  webAppUrl?: string;
}) {
  log.botLog("Menu button verification", {
    operation: "bot_configuration",
    component: "menu_button_verification",
    menuButtonType: params.menuButtonType,
    menuButtonText: params.menuButtonText,
    webAppUrl: params.webAppUrl,
  });
}

export function logCommandsConfigured() {
  log.botLog("Commands configured", {
    operation: "bot_configuration",
    component: "commands",
    status: "success",
  });
}

export function logBotDescriptionConfigured(params: {
  description: string;
  shortDescription: string;
}) {
  log.botLog("Bot description configured", {
    operation: "bot_configuration",
    component: "description",
    status: "success",
    description: params.description.substring(0, 100) + "...",
    shortDescription: params.shortDescription,
  });
}

export function logBotSetupCompleted() {
  log.botLog("Bot setup completed successfully", {
    operation: "bot_startup",
    status: "completed",
    features: [
      "My Buy Orders",
      "Contact Support",
      "Open Marketplace (Web App)",
    ],
  });
}

export function logBotStopping() {
  log.botLog("Stopping bot", {
    operation: "bot_shutdown",
    step: "bot",
  });
}
