import { log } from "../../utils/logger";

export function logCancelledGiftProcessingStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Processing cancelled gift retrieval", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logOrderNotFoundForCancelledGift(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("Order not found for cancelled gift retrieval", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logOrderNotCancelled(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  orderStatus: string;
}) {
  log.info("Order is not cancelled, cannot retrieve gift", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logUserNotAuthorizedForCancelledGift(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  originalSellerId?: string;
}) {
  log.warn("User not authorized - not original seller for cancelled gift", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logNoGiftToTransferBack(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("No gift available to transfer back for cancelled order", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logMissingBusinessConnectionForCancelledGift(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.error("Missing business connection ID for cancelled gift transfer", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logCancelledGiftTransferStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  businessConnectionId: string;
  giftToTransferBack: string;
}) {
  log.info("Starting cancelled gift transfer back to seller", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logGiftFieldResetSuccess(params: { orderId: string }) {
  log.info("Gift field reset successfully for cancelled order", {
    operation: "get_cancelled_gift",
    ...params,
  });
}

export function logGiftFieldResetError(params: {
  orderId: string;
  error: unknown;
}) {
  log.error("Failed to reset gift field for cancelled order", params.error, {
    operation: "get_cancelled_gift",
    orderId: params.orderId,
  });
}

export function logCancelledGiftTransferCompleted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Cancelled gift transfer completed successfully", {
    operation: "get_cancelled_gift",
    ...params,
  });
}
