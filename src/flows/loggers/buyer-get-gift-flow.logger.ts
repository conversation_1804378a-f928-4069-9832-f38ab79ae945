import { log } from "../../utils/logger";

export function logBuyerGiftRequestStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Processing buyer gift request", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logOrderNotFoundForBuyerRequest(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("No existing order found for buyer request", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logUserNotAuthorizedBuyer(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  originalBuyerId?: string;
}) {
  log.warn("User not authorized - not original buyer", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logOrderNotReadyForGiftDelivery(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  orderStatus: string;
}) {
  log.info("Order is not ready for gift delivery", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logNoGiftAvailableForBuyer(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("No gift available to transfer to buyer", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logMissingBusinessConnectionForBuyer(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.error("Missing business connection ID for gift transfer", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logGiftTransferToBuyerStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  businessConnectionId: string;
  giftToTransferToBuyer: string;
}) {
  log.info("Starting gift transfer to buyer", {
    operation: "buyer_get_gift",
    ...params,
  });
}

export function logGiftTransferToBuyerCompleted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Gift transfer to buyer completed successfully", {
    operation: "buyer_get_gift",
    ...params,
  });
}
