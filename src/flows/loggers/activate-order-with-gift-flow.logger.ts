import { log } from "../../utils/logger";

export function logActivateOrderStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Activate order with gift flow started", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logOrderNotFound(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("Order not found for activation", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logOrderNotCreatedStatus(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  currentStatus: string;
}) {
  log.warn("Order is not in CREATED status for activation", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logUserNotSeller(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  sellerId?: string;
}) {
  log.warn("User is not the seller of the order", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logGiftValidationFailed(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  collectionId: string;
  uniqueGift: any;
  validationMessage?: string;
}) {
  log.warn("Gift validation failed for CREATED order activation", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logGiftValidationError(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  error: unknown;
}) {
  log.error(
    "Gift validation failed with exception for CREATED order activation",
    params.error,
    {
      operation: "activate_order_with_gift",
      chat_id: params.chat_id,
      userId: params.userId,
      pendingOrderId: params.pendingOrderId,
    }
  );
}

export function logOrderActivationSuccess(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  collectionId: string;
}) {
  log.info("Order activated successfully", {
    operation: "activate_order_with_gift",
    ...params,
  });
}

export function logOrderActivationError(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  error: unknown;
}) {
  log.error("Error activating order", params.error, {
    operation: "activate_order_with_gift",
    chat_id: params.chat_id,
    userId: params.userId,
    pendingOrderId: params.pendingOrderId,
  });
}
