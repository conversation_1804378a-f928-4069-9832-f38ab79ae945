import { log } from "../../utils/logger";

export function logSendGiftToPaidOrderStarted(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.info("Send gift to paid order flow started", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logOrderNotFound(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
}) {
  log.warn("Order not found for pending order ID", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logOrderNotPaidStatus(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  currentStatus: string;
}) {
  log.warn("Order is not in PAID status for gift sending", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logUserNotSeller(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  sellerId?: string;
}) {
  log.warn("User is not the seller of the order", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logGiftValidationFailed(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  collectionId: string;
  uniqueGift: any;
}) {
  log.warn("Gift validation failed for paid order", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logGiftValidationError(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  error: unknown;
}) {
  log.error(
    "Gift validation failed with exception for paid order",
    params.error,
    {
      operation: "send_gift_to_paid_order",
      chat_id: params.chat_id,
      userId: params.userId,
      pendingOrderId: params.pendingOrderId,
    }
  );
}

export function logBuyerNotificationSent(params: {
  orderId: string;
  buyerTgId: string;
  orderNumber: number;
}) {
  log.info("Buyer notification sent successfully for paid order", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}

export function logBuyerNotificationError(params: {
  orderId: string;
  buyerTgId?: string;
  error: unknown;
}) {
  log.error("Failed to send buyer notification for paid order", params.error, {
    operation: "send_gift_to_paid_order",
    orderId: params.orderId,
    buyerTgId: params.buyerTgId,
  });
}

export function logGiftSentToRelayerSuccess(params: {
  chat_id: string | number;
  userId: string;
  pendingOrderId: string;
  collectionId: string;
}) {
  log.info("Gift sent to relayer successfully for paid order", {
    operation: "send_gift_to_paid_order",
    ...params,
  });
}
