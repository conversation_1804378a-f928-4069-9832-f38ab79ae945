import { Context } from "telegraf";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import { sendGiftToRelayer } from "../../firebase-service";
import { log } from "../../utils/logger";
import { OrderGift } from "../../miker<PERSON>nko/marketplace-shared";

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  gift: OrderGift,
  chat_id: number
) => {
  try {
    log.info("Starting gift to relayer processing", {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
    });

    await ctx.telegram.sendMessage(
      chat_id,
      T(ctx, botMessages.processingGift.id)
    );

    const result = await sendGiftToRelayer(orderId, gift);

    log.info("Send gift to relayer result", {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
      success: result.success,
    });

    if (result.success) {
      await ctx.telegram.sendMessage(
        chat_id,
        T(ctx, botMessages.giftSentSuccess.id)
      );
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        T(ctx, botMessages.giftSentError.id)
      );
    }
  } catch (error) {
    log.error("Error in handleGiftToRelayer", error, {
      operation: "handle_gift_to_relayer",
      orderId,
      gift,
      chat_id,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      T(ctx, botMessages.giftSentError.id)
    );
  }
};
