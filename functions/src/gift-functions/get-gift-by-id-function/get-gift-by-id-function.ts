import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import { getGiftById } from "./get-gift-by-id-function.service";
import { logGetGiftByIdError } from "./get-gift-by-id-function.logger";
import { throwGetGiftByIdInternalError } from "./get-gift-by-id-function.error-handler";

export const getGiftByIdByBot = onCall<{
  giftId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { giftId, botToken } = request.data;

  try {
    return await getGiftById({ giftId, botToken });
  } catch (error) {
    logGetGiftByIdError({
      error,
      giftId: request.data.giftId,
    });
    throwGetGiftByIdInternalError((error as any).message);
  }
  return null;
});
