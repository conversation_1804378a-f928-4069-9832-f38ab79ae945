import * as admin from "firebase-admin";
import {
  GIFTS_COLLECTION_NAME,
  GiftEntity,
} from "../../mikerudenko/marketplace-shared";
import { validateBotTokenSimple } from "../../services/bot-validation.service";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwGiftIdRequired,
} from "./get-gift-by-id-function.error-handler";

function validateGiftId(giftId: string) {
  if (!giftId) {
    throwGiftIdRequired();
  }
}

function validateBotToken(botToken: string) {
  try {
    validateBotTokenSimple(botToken);
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Bot token is required")
    ) {
      throwBotTokenRequired();
    } else {
      throwInvalidBotToken();
    }
  }
}

export async function getGiftById(params: {
  giftId: string;
  botToken: string;
}): Promise<GiftEntity | null> {
  const { giftId, botToken } = params;

  validateGiftId(giftId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();

  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}
