import { HttpsError } from "firebase-functions/v2/https";

export function throwBotTokenRequired() {
  throw new HttpsError("invalid-argument", "Bot token is required");
}

export function throwInvalidBotToken() {
  throw new HttpsError("permission-denied", "Invalid bot token");
}

export function throwGiftIdRequired() {
  throw new HttpsError("invalid-argument", "Gift ID is required");
}

export function throwGiftNotFound() {
  throw new HttpsError("not-found", "Gift not found");
}

export function throwGetGiftByIdInternalError(message: string) {
  throw new HttpsError("internal", `Get gift by ID failed: ${message}`);
}
