import { onCall, HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";
import { getUserGiftsAvailableForWithdrawalService as getUserGiftsAvailableForWithdrawal } from "./get-user-gifts-available-for-withdrawal-function.service";
import { handleGetUserGiftsAvailableForWithdrawalError } from "./get-user-gifts-available-for-withdrawal-function.error-handler";
import { commonFunctionsConfig } from "../../constants";
import { validateBotTokenSimple } from "../../services/bot-validation.service";

export const getUserGiftsAvailableForWithdrawalByBot = onCall<{
  tg_id: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  try {
    const { tg_id, botToken } = request.data;

    if (!tg_id) {
      throw new HttpsError("invalid-argument", "Telegram ID is required");
    }

    if (!botToken) {
      throw new HttpsError("invalid-argument", "Bot token is required");
    }

    // Validate bot token
    validateBotTokenSimple(botToken);

    logger.info("Getting user gifts available for withdrawal", {
      tg_id,
    });

    const gifts = await getUserGiftsAvailableForWithdrawal(tg_id);

    logger.info("Successfully retrieved user gifts available for withdrawal", {
      tg_id,
      giftsCount: gifts.length,
    });

    return { gifts };
  } catch (error) {
    return handleGetUserGiftsAvailableForWithdrawalError(error, request.data);
  }
});
