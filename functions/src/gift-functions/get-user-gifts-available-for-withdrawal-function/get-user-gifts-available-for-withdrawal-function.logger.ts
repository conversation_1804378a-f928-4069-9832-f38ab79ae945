import { logger } from "firebase-functions/v2";

export const logGetUserGiftsAvailableForWithdrawalStart = (tgId: string) => {
  logger.info("Starting getUserGiftsAvailableForWithdrawal function", {
    tgId,
    timestamp: new Date().toISOString(),
  });
};

export const logGetUserGiftsAvailableForWithdrawalSuccess = (
  tgId: string,
  giftsCount: number
) => {
  logger.info(
    "Successfully completed getUserGiftsAvailableForWithdrawal function",
    {
      tgId,
      giftsCount,
      timestamp: new Date().toISOString(),
    }
  );
};

export const logGetUserGiftsAvailableForWithdrawalError = (
  tgId: string,
  error: string
) => {
  logger.error("Error in getUserGiftsAvailableForWithdrawal function", {
    tgId,
    error,
    timestamp: new Date().toISOString(),
  });
};
