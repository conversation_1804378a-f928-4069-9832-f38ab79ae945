import { HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";

export const handleGetUserGiftsAvailableForWithdrawalError = (
  error: unknown,
  requestData: any
): never => {
  const errorMessage = error instanceof Error ? error.message : String(error);

  logger.error("Error in getUserGiftsAvailableForWithdrawal function", {
    error: errorMessage,
    requestData,
    stack: error instanceof Error ? error.stack : undefined,
  });

  if (error instanceof HttpsError) {
    throw error;
  }

  throw new HttpsError(
    "internal",
    "Failed to get user gifts available for withdrawal"
  );
};
