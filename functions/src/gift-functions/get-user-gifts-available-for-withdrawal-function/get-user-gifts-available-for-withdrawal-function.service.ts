import { firestore } from "firebase-admin";
import {
  GiftEntity,
  OrderStatus,
  GiftStatus,
  OrderEntity,
} from "../../mikerudenko/marketplace-shared";
import { logger } from "firebase-functions/v2";

const db = firestore();

export interface GiftWithOrderInfo extends GiftEntity {
  relatedOrder?: {
    id: string;
    number: number;
    status: string;
    price: number;
  } | null;
}

export const getUserGiftsAvailableForWithdrawalService = async (
  tgId: string
): Promise<GiftWithOrderInfo[]> => {
  try {
    logger.info("Starting getUserGiftsAvailableForWithdrawalService", { tgId });

    // Get all gifts owned by the user with status 'deposited'
    const giftsQuery = await db
      .collection("gifts")
      .where("owner_tg_id", "==", tgId)
      .where("status", "==", GiftStatus.DEPOSITED)
      .get();

    const gifts = giftsQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GiftEntity[];

    logger.info("Found gifts for user", { tgId, totalGifts: gifts.length });

    // Get all orders to check gift relationships
    const ordersQuery = await db.collection("orders").get();
    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    logger.info("Retrieved all orders for filtering", {
      totalOrders: orders.length,
    });

    // Filter gifts and include order information
    const availableGifts: GiftWithOrderInfo[] = [];

    for (const gift of gifts) {
      // Find orders related to this gift
      const relatedOrders = orders.filter((order) => order.giftId === gift.id);
      let relatedOrder: GiftWithOrderInfo["relatedOrder"] = null;
      let isAvailable = false;

      if (relatedOrders.length === 0) {
        // Gift not linked to any order - available for withdrawal
        logger.debug("Gift not linked to any order", { giftId: gift.id });
        isAvailable = true;
      } else {
        // First, check for any exclusionary conditions (orders that prevent withdrawal)
        const hasActiveOrder = relatedOrders.some(
          (order) =>
            (order.status === OrderStatus.ACTIVE ||
              order.status === OrderStatus.PAID ||
              order.status === OrderStatus.CREATED) &&
            order.sellerId === tgId
        );

        if (hasActiveOrder) {
          logger.debug(
            "Gift linked to active/paid/created order where user is seller - not available",
            {
              giftId: gift.id,
              activeOrders: relatedOrders
                .filter(
                  (order) =>
                    (order.status === OrderStatus.ACTIVE ||
                      order.status === OrderStatus.PAID ||
                      order.status === OrderStatus.CREATED) &&
                    order.sellerId === tgId
                )
                .map((order) => ({ id: order.id, status: order.status })),
            }
          );
          isAvailable = false;
        } else {
          // No exclusionary conditions, check for withdrawal-eligible conditions
          for (const order of relatedOrders) {
            // Gifts linked to orders with status 'gift_sent_to_relayer' where user is buyer
            if (
              order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
              order.buyerId === tgId
            ) {
              logger.debug(
                "Gift linked to gift_sent_to_relayer order where user is buyer",
                {
                  giftId: gift.id,
                  orderId: order.id,
                }
              );
              isAvailable = true;
              relatedOrder = {
                id: order.id!,
                number: order.number,
                status: order.status,
                price: order.price,
              };
              break;
            }

            // Gifts linked to cancelled orders where user is seller
            if (
              order.status === OrderStatus.CANCELLED &&
              order.sellerId === tgId
            ) {
              logger.debug(
                "Gift linked to cancelled order where user is seller",
                {
                  giftId: gift.id,
                  orderId: order.id,
                }
              );
              isAvailable = true;
              relatedOrder = {
                id: order.id!,
                number: order.number,
                status: order.status,
                price: order.price,
              };
              break;
            }
          }

          // If no specific withdrawal conditions were met, default to not available
          if (!isAvailable) {
            logger.debug(
              "Gift linked to orders but no withdrawal conditions met",
              {
                giftId: gift.id,
                orderStatuses: relatedOrders.map((order) => ({
                  id: order.id,
                  status: order.status,
                  sellerId: order.sellerId,
                  buyerId: order.buyerId,
                })),
              }
            );
          }
        }
      }

      if (isAvailable) {
        availableGifts.push({
          ...gift,
          relatedOrder,
        });
      }
    }

    logger.info("Filtered gifts available for withdrawal", {
      tgId,
      availableGiftsCount: availableGifts.length,
    });

    return availableGifts;
  } catch (error) {
    logger.error("Error in getUserGiftsAvailableForWithdrawalService", {
      tgId,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
};
