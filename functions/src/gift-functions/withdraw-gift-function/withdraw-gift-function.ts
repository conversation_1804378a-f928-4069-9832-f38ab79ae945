import { onCall, HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";
import { withdrawGiftService } from "./withdraw-gift-function.service";
import { handleWithdrawGiftError } from "./withdraw-gift-function.error-handler";
import { commonFunctionsConfig } from "../../constants";
import { validateBotTokenSimple } from "../../services/bot-validation.service";

export const withdrawGiftByBot = onCall<{
  giftId: string;
  userTgId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  try {
    const { giftId, userTgId, botToken } = request.data;

    if (!giftId) {
      throw new HttpsError("invalid-argument", "Gift ID is required");
    }

    if (!userTgId) {
      throw new HttpsError("invalid-argument", "User Telegram ID is required");
    }

    if (!botToken) {
      throw new HttpsError("invalid-argument", "Bot token is required");
    }

    // Validate bot token
    validateBotTokenSimple(botToken);

    logger.info("Processing gift withdrawal", {
      giftId,
      userTgId,
    });

    const result = await withdrawGiftService({ giftId, userTgId });

    logger.info("Gift withdrawal processed", {
      giftId,
      userTgId,
      success: result.success,
    });

    return result;
  } catch (error) {
    return handleWithdrawGiftError(error, request.data);
  }
});
