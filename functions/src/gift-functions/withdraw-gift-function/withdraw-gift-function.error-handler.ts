import { logger } from "firebase-functions/v2";
import { WithdrawGiftResult } from "./withdraw-gift-function.service";

export function handleWithdrawGiftError(
  error: any,
  requestData: any
): WithdrawGiftResult {
  logger.error("Error in withdrawGiftByBot", {
    error: error.message || error,
    requestData,
  });

  // Handle specific error types
  if (error.code === "invalid-argument") {
    return {
      success: false,
      error: error.message || "Invalid request parameters",
    };
  }

  if (error.code === "permission-denied") {
    return {
      success: false,
      error: "Permission denied",
    };
  }

  if (error.code === "not-found") {
    return {
      success: false,
      error: "Resource not found",
    };
  }

  // Default error response
  return {
    success: false,
    error: "Internal error occurred during gift withdrawal",
  };
}
