import { createServiceLogger } from "../logger/base-logger";

const tonMonitorLogger = createServiceLogger("ton-monitor-function");

export function logMonitorTriggered(data: {
  status: string;
  timestamp: string;
}) {
  tonMonitorLogger.logInfo("TON monitor triggered", "monitor", data);
}

export function logMonitorCompleted() {
  tonMonitorLogger.logInfo("TON monitor completed", "monitor", {});
}

export function logMonitorFailed(data: { error: unknown; status: string }) {
  tonMonitorLogger.logError("TON monitor failed", data.error, "monitor", {
    status: data.status,
  });
}

export function logTransactionProcessed(data: {
  transactionHash: string;
  status: string;
}) {
  tonMonitorLogger.logInfo(
    `Transaction processed: ${data.transactionHash}`,
    "transaction",
    data
  );
}

export function logMonitorError(data: { error: unknown }) {
  tonMonitorLogger.logError("Error in TON monitor", data.error, "monitor", {});
}

export function logUserLookup(data: {
  tonWalletAddress: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "Looking for user with TON address",
    "user_lookup",
    data
  );
}

export function logUserFound(data: {
  tonWalletAddress: string;
  userId: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "Found exact match for address",
    "user_lookup",
    data
  );
}

export function logInvalidAddress(data: {
  tonWalletAddress: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo("Invalid address format", "user_lookup", data);
}

export function logRawAddressSearch(data: {
  tonWalletAddress: string;
  rawAddress: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "No exact match found, searching by raw address part",
    "user_lookup",
    data
  );
}

export function logUserFoundByRaw(data: {
  userTonWallet: string;
  searchedAddress: string;
  userId: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo("Found user by raw address", "user_lookup", data);
}

export function logUserNotFound(data: {
  tonWalletAddress: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo("No user found for address", "user_lookup", data);
}

export function logTransactionFiltering(data: {
  monitor: string;
  count?: number;
  totalTransactions?: number;
  newTransactions?: number;
  significantCount?: number;
  minThreshold?: number;
}) {
  tonMonitorLogger.logInfo(
    "Transaction filtering",
    "transaction_filtering",
    data
  );
}

export function logTonApiCall(data: {
  endpoint?: string;
  limit?: number;
  network?: string;
  operation: string;
  requestBody?: any;
  count?: number;
}) {
  tonMonitorLogger.logInfo("TON API call", "ton_api_call", data);
}

export function logTonApiError(data: { error: unknown; operation: string }) {
  tonMonitorLogger.logError(
    "Error fetching TON transactions",
    data.error,
    "ton_api_call",
    {}
  );
}

export function logTransactionExtraction(data: {
  sender: string;
  amount: number;
  transactionId: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "Extracting transaction info",
    "transaction_extraction",
    data
  );
}

export function logBalanceUpdate(data: {
  userId: string;
  amount: number;
  originalAmount: number;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "Updated balance for user after deposit",
    "deposit_processing",
    data
  );
}

export function logBalanceUpdateError(data: {
  error: unknown;
  userId: string;
  operation: string;
}) {
  tonMonitorLogger.logError(
    "Error updating balance for user",
    data.error,
    "deposit_processing",
    { userId: data.userId }
  );
}

export function logMonitorStatus(data: {
  monitor: string;
  status: string;
  count?: number;
  lastCheckedLt?: string;
  latestLt?: string;
}) {
  tonMonitorLogger.logInfo(`TON monitor: ${data.status}`, "monitor", data);
}

export function logWalletNotFound(data: {
  walletAddress: string;
  transactionId: string;
  operation: string;
}) {
  tonMonitorLogger.logInfo(
    "No user found for wallet address",
    "transaction_processing",
    data
  );
}
