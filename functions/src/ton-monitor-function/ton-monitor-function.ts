import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { monitorTonTransactions } from "./ton-monitor-function.service";
import {
  logMonitorTriggered,
  logMonitorCompleted,
  logMonitorFailed,
} from "./ton-monitor-function.logger";

export const tonMonitor = onSchedule(
  {
    schedule: "*/5 * * * *", // Every 5 minutes
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await monitorTonTransactions();
      logMonitorCompleted();
    } catch (error) {
      logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
