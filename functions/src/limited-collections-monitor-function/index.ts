export { limitedCollectionsMonitor } from "./limited-collections-monitor-function";

export {
  logMonitorStarted,
  logCollectionsFound,
  logCollectionProcessed,
  logMonitorError,
  logMonitorTriggered,
  logMonitorCompleted,
  logMonitorFailed,
} from "./limited-collections-monitor-function.logger";

export {
  throwTelegramClientError,
  throwCollectionUpdateError,
  throwLimitedCollectionsMonitorError,
} from "./limited-collections-monitor-function.error-handler";

// Types are now inlined in function parameters
