import { onSchedule } from "firebase-functions/v2/scheduler";
import { commonFunctionsConfig } from "../constants";
import { checkLimitedCollections } from "./limited-collections-monitor-function.service";
import {
  logMonitorTriggered,
  logMonitorCompleted,
  logMonitorFailed,
} from "./limited-collections-monitor-function.logger";

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 */6 * * *", // Every 6 hours
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      logMonitorTriggered({
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await checkLimitedCollections();
      logMonitorCompleted();
    } catch (error) {
      logMonitorFailed({
        error,
        status: "monitor_failed",
      });
    }
  }
);
