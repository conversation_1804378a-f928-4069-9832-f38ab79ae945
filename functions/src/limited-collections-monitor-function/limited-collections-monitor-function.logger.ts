import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const limitedCollectionsMonitorLogger = createServiceLogger(
  "limited-collections-monitor-function"
);

export function logMonitorStarted({ status }: { status: string }) {
  limitedCollectionsMonitorLogger.logInfo(
    "Limited collections monitor started",
    "monitor",
    { status }
  );
}

export function logCollectionsFound({ count }: { count: number }) {
  limitedCollectionsMonitorLogger.logInfo(
    `Found ${count} collections to process`,
    "monitor",
    { count }
  );
}

export function logCollectionProcessed({
  collectionId,
  status,
}: {
  collectionId: string;
  status: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    `Processed collection ${collectionId}`,
    "monitor",
    { collectionId, status }
  );
}

export function logMonitorError({ error }: { error: unknown }) {
  limitedCollectionsMonitorLogger.logError(
    "Error in limited collections monitor",
    error,
    "monitor",
    {}
  );
}

export function logMonitorTriggered({
  status,
  timestamp,
}: {
  status: string;
  timestamp: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Limited collections monitor triggered",
    "monitor",
    { status, timestamp }
  );
}

export function logMonitorCompleted() {
  limitedCollectionsMonitorLogger.logInfo(
    "Limited collections monitor completed",
    "monitor",
    {}
  );
}

export function logMonitorFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Limited collections monitor failed",
    error,
    "monitor",
    { status }
  );
}

export function logNoGiftsFound() {
  limitedCollectionsMonitorLogger.logInfo(
    "No gifts found in result",
    "telegram_api",
    {}
  );
}

export function logLimitedGiftsFound({ count }: { count: number }) {
  limitedCollectionsMonitorLogger.logInfo(
    "Found limited gifts from Telegram API",
    "telegram_api",
    { count }
  );
}

export function logTelegramApiError({ error }: { error: unknown }) {
  limitedCollectionsMonitorLogger.logError(
    "Error fetching limited collections from Telegram",
    error,
    "telegram_api",
    {}
  );
}

export function logCollectionNotFound({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Collection not found when trying to update to MARKET",
    new Error("Collection not found"),
    "collection_update",
    { collectionId }
  );
}

export function logLaunchedAtSet({ collectionId }: { collectionId: string }) {
  limitedCollectionsMonitorLogger.logInfo(
    "Setting launchedAt for collection",
    "collection_update",
    { collectionId }
  );
}

export function logLaunchedAtExists({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Collection already has launchedAt, skipping",
    "collection_update",
    { collectionId }
  );
}

export function logCollectionUpdatedToMarket({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Updated collection to MARKET status",
    "collection_update",
    { collectionId }
  );
}

export function logNewCollectionCreated({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Created new collection in Firestore",
    "collection_creation",
    { collectionId }
  );
}

export function logCollectionCreationNotFound({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Collection not found in Firestore, creating new collection",
    "collection_creation",
    { collectionId }
  );
}

export function logCollectionCreationFailed({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Failed to create collection",
    new Error("Collection creation failed"),
    "collection_creation",
    { collectionId }
  );
}

export function logCollectionStatusNotPremarket({
  collectionId,
  status,
}: {
  collectionId: string;
  status: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Collection status is not PREMARKET, skipping",
    "collection_processing",
    { collectionId, status }
  );
}

export function logProcessingCollectionWithUpgradeStars({
  collectionId,
  upgradeStars,
}: {
  collectionId: string;
  upgradeStars: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Processing collection with upgradeStars",
    "collection_processing",
    { collectionId, upgradeStars }
  );
}

export function logCollectionAlreadyExists({
  collectionId,
}: {
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logInfo(
    "Collection already exists in Firestore",
    "collection_existence",
    { collectionId }
  );
}

export function logEnsureCollectionError({
  error,
  collectionId,
}: {
  error: unknown;
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Error ensuring collection exists",
    error,
    "collection_existence",
    { collectionId }
  );
}

export function logNoLimitedCollections() {
  limitedCollectionsMonitorLogger.logInfo(
    "No limited collections found from Telegram API",
    "monitor",
    {}
  );
}

export function logEnsureCollectionFailed({
  error,
  collectionId,
}: {
  error: unknown;
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Failed to ensure collection exists",
    error,
    "collection_existence",
    { collectionId }
  );
}

export function logNoUpgradeableCollections() {
  limitedCollectionsMonitorLogger.logInfo(
    "No upgradeable limited collections found",
    "monitor",
    {}
  );
}

export function logUpgradeableCollectionsFound({ count }: { count: number }) {
  limitedCollectionsMonitorLogger.logInfo(
    "Found upgradeable limited collections",
    "monitor",
    { count }
  );
}

export function logProcessCollectionFailed({
  error,
  collectionId,
}: {
  error: unknown;
  collectionId: string;
}) {
  limitedCollectionsMonitorLogger.logError(
    "Failed to process collection",
    error,
    "collection_processing",
    { collectionId }
  );
}
