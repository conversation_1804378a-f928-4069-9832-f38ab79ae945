import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const expiredOrdersMonitorLogger = createServiceLogger(
  "expired-orders-monitor-function"
);

export function logProcessingStarted() {
  expiredOrdersMonitorLogger.logInfo(
    "Starting expired orders processing",
    "process_expired",
    {}
  );
}

export function logNoOrdersFound() {
  expiredOrdersMonitorLogger.logInfo(
    "No expired orders found",
    "process_expired",
    {}
  );
}

export function logOrdersFound({ count }: { count: number }) {
  expiredOrdersMonitorLogger.logInfo(
    `Found ${count} expired orders to process`,
    "process_expired",
    { count }
  );
}

export function logOrderSkipped({
  orderId,
  reason,
}: {
  orderId: string;
  reason: string;
}) {
  expiredOrdersMonitorLogger.logInfo(
    `Skipped order ${orderId}: ${reason}`,
    "process_expired",
    { orderId, reason }
  );
}

export function logOrderProcessed({
  orderId,
  status,
  message,
}: {
  orderId: string;
  status: string;
  message: string;
}) {
  expiredOrdersMonitorLogger.logInfo(
    `Processed order ${orderId}: ${message}`,
    "process_expired",
    { orderId, status, message }
  );
}

export function logOrderProcessError({
  error,
  orderId,
}: {
  error: unknown;
  orderId: string;
}) {
  expiredOrdersMonitorLogger.logError(
    `Error processing order ${orderId}`,
    error,
    "process_expired",
    { orderId }
  );
}

export function logProcessingCompleted(): void {
  expiredOrdersMonitorLogger.logInfo(
    "Expired orders processing completed",
    "process_expired",
    {}
  );
}

export function logProcessingError({ error }: { error: unknown }) {
  expiredOrdersMonitorLogger.logError(
    "Error in expired orders processing",
    error,
    "process_expired",
    {}
  );
}

export function logMonitorTriggered({
  status,
  timestamp,
}: {
  status: string;
  timestamp: string;
}) {
  expiredOrdersMonitorLogger.logInfo(
    "Expired orders monitor triggered",
    "monitor",
    { status, timestamp }
  );
}

export function logMonitorCompleted() {
  expiredOrdersMonitorLogger.logInfo(
    "Expired orders monitor completed",
    "monitor",
    {}
  );
}

export function logMonitorFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  expiredOrdersMonitorLogger.logError(
    "Expired orders monitor failed",
    error,
    "monitor",
    { status }
  );
}
