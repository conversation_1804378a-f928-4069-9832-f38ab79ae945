import * as admin from "firebase-admin";
import {
  OrderEntity,
  OrderStatus,
  ORDERS_COLLECTION_NAME,
} from "../mikerudenko/marketplace-shared";
import { processOrderCancellation } from "../services/order-cancellation-service/order-cancellation-service";
import {
  logProcessingStarted,
  logNoOrdersFound,
  logOrdersFound,
  logOrderSkipped,
  logOrderProcessed,
  logOrderProcessError,
  logProcessingCompleted,
  logProcessingError,
} from "./expired-orders-monitor-function.logger";

const db = admin.firestore();

export async function processExpiredOrders(): Promise<void> {
  try {
    logProcessingStarted();

    const now = admin.firestore.Timestamp.now();

    // Only cancel orders where sellers failed to send gifts to relayer (status='paid')
    // Orders with status='gift_sent_to_relayer' are not cancelled - buyers can take gifts after deadline
    const expiredOrdersQuery = db
      .collection(ORDERS_COLLECTION_NAME)
      .where("status", "==", OrderStatus.PAID)
      .where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      logNoOrdersFound();
      return;
    }

    logOrdersFound({ count: expiredOrdersSnapshot.size });

    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        logOrderSkipped({
          orderId: order.id || "unknown",
          reason: "missing_buyer_or_seller",
        });
        continue;
      }

      try {
        const result = await processOrderCancellation(order, order.sellerId);
        logOrderProcessed({
          orderId: order.id || "unknown",
          status: "processed",
          message: result.message,
        });
      } catch (error) {
        logOrderProcessError({
          error,
          orderId: order.id || "unknown",
        });
      }
    }

    logProcessingCompleted();
  } catch (error) {
    logProcessingError({ error });
    throw error;
  }
}
