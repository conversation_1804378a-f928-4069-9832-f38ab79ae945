import { createServiceLogger } from "../logger/base-logger";

const txLookupLogger = createServiceLogger("tx-lookup-function");

export function logTxLookupGetError(data: { error: unknown }) {
  txLookupLogger.logError(
    "Error getting tx lookup",
    data.error,
    "get_tx_lookup",
    {}
  );
}

export function logTxLookupUpdated(data: { lastCheckedRecordId: string }) {
  txLookupLogger.logInfo(
    `Updated tx lookup with last_checked_record_id: ${data.lastCheckedRecordId}`,
    "update_tx_lookup",
    {
      lastCheckedRecordId: data.lastCheckedRecordId,
    }
  );
}

export function logTxLookupUpdateError(data: {
  error: unknown;
  lastCheckedRecordId: string;
}) {
  txLookupLogger.logError(
    "Error updating tx lookup",
    data.error,
    "update_tx_lookup",
    {
      lastCheckedRecordId: data.lastCheckedRecordId,
    }
  );
}
