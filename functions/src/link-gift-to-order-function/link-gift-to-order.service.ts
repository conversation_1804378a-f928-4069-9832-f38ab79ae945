import * as admin from "firebase-admin";
import {
  AppDate,
  formatDateToFirebaseTimestamp,
  OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from "../mikerudenko/marketplace-shared";
import { validateGiftForLinking } from "../services/gift-validation.service";
import { getUserById } from "../services/user-lookup.service";
import {
  throwGiftCollectionMismatch,
  throwGiftNotOwnedByUser,
  throwInvalidOrderStatus,
  throwOrderNotFound,
  throwOrderNotOwnedByUser,
} from "./link-gift-to-order.error-handler";
import {
  logGiftLinkedToOrder,
  logGiftValidation,
  logOrderValidation,
} from "./link-gift-to-order.logger";

import { processPaidOrderWithGift } from "../services/order-gift-processing.service";

export interface LinkGiftToOrderParams {
  giftId: string;
  orderId: string;
  userId: string;
}

export interface LinkGiftToOrderResult {
  success: boolean;
  message: string;
  order?: OrderEntity;
}

export async function linkGiftToOrderService({
  giftId,
  orderId,
  userId,
}: LinkGiftToOrderParams): Promise<LinkGiftToOrderResult> {
  const db = admin.firestore();

  // 1. Validate gift (existence, status, not used in other orders)
  let gift;
  try {
    gift = await validateGiftForLinking(giftId);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === "Gift not found") {
        throwOrderNotFound(); // Reuse existing error handler
      } else if (error.message.includes("status")) {
        throwInvalidOrderStatus("Gift must have status 'deposited'");
      } else if (error.message.includes("already linked")) {
        throwGiftCollectionMismatch(); // Reuse for gift already used
      }
    }
    throw error;
  }

  logGiftValidation({
    giftId,
    giftOwnerId: gift.owner_tg_id,
    giftCollectionId: gift.collectionId,
  });

  // 2. Get user to validate gift ownership
  const user = await getUserById(userId);
  if (!user || user.tg_id !== gift.owner_tg_id) {
    throwGiftNotOwnedByUser();
  }

  // 3. Get and validate order
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();
  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (!order.sellerId) {
    throw new Error("Order has no seller ID");
  }
  logOrderValidation({
    orderId,
    orderSellerId: order.sellerId,
    orderStatus: order.status,
    orderCollectionId: order.collectionId,
    userId,
  });

  // 4. Validate user is the seller of the order
  if (order.sellerId !== userId) {
    throwOrderNotOwnedByUser();
  }

  // 5. Validate order status (must be CREATED or PAID)
  if (
    order.status !== OrderStatus.CREATED &&
    order.status !== OrderStatus.PAID
  ) {
    throwInvalidOrderStatus(order.status);
  }

  // 6. Validate gift and order are from the same collection
  if (gift.collectionId !== order.collectionId) {
    throwGiftCollectionMismatch();
  }

  // 7. Link gift to order based on order status
  const batch = db.batch();

  if (order.status === OrderStatus.CREATED) {
    // For CREATED orders, activate the order
    batch.update(orderDoc.ref, {
      giftId,
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    await batch.commit();

    logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.ACTIVE,
    });

    return {
      success: true,
      message: "Gift linked to order and order activated successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    // For PAID orders, use reusable function to process paid order with gift
    await processPaidOrderWithGift({
      order,
      giftId,
      orderDoc,
      batch,
    });

    logGiftLinkedToOrder({
      giftId,
      orderId,
      userId,
      newOrderStatus: OrderStatus.GIFT_SENT_TO_RELAYER,
    });

    return {
      success: true,
      message: "Gift linked to paid order and sent to relayer successfully.",
      order: {
        ...order,
        giftId,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  }
}
