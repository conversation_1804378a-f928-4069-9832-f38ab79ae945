import { logger } from "firebase-functions/v2";

export function logLinkGiftToOrderFunctionCalled(context: {
  giftId?: string;
  orderId?: string;
  userId?: string;
}) {
  logger.info("Link gift to order function called", {
    operation: "link_gift_to_order_start",
    giftId: context.giftId,
    orderId: context.orderId,
    userId: context.userId,
  });
}

export function logGiftValidation(context: {
  giftId: string;
  giftOwnerId: string;
  giftCollectionId: string;
}) {
  logger.info("Gift validation completed", {
    operation: "gift_validation",
    giftId: context.giftId,
    giftOwnerId: context.giftOwnerId,
    giftCollectionId: context.giftCollectionId,
  });
}

export function logOrderValidation(context: {
  orderId: string;
  orderSellerId: string;
  orderStatus: string;
  orderCollectionId: string;
  userId: string;
}) {
  logger.info("Order validation completed", {
    operation: "order_validation",
    orderId: context.orderId,
    orderSellerId: context.orderSellerId,
    orderStatus: context.orderStatus,
    orderCollectionId: context.orderCollectionId,
    userId: context.userId,
  });
}

export function logGiftLinkedToOrder(context: {
  giftId: string;
  orderId: string;
  userId: string;
  newOrderStatus: string;
}) {
  logger.info("Gift successfully linked to order", {
    operation: "gift_linked_to_order",
    giftId: context.giftId,
    orderId: context.orderId,
    userId: context.userId,
    newOrderStatus: context.newOrderStatus,
  });
}

export function logLinkGiftToOrderCompleted(context: {
  giftId: string;
  orderId: string;
  userId: string;
  success: boolean;
}) {
  logger.info("Link gift to order function completed", {
    operation: "link_gift_to_order_complete",
    giftId: context.giftId,
    orderId: context.orderId,
    userId: context.userId,
    success: context.success,
  });
}

export function logLinkGiftToOrderError(error: unknown) {
  logger.error("Error in link gift to order function", {
    operation: "link_gift_to_order_error",
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
  });
}
