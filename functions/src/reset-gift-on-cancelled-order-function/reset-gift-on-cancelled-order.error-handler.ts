import { HttpsError } from "firebase-functions/v2/https";
import { logResetGiftServiceError } from "./reset-gift-on-cancelled-order.logger";

export function throwBotTokenNotConfiguredError(): never {
  throw new HttpsError("internal", "Bot token not configured");
}

export function throwInvalidBotTokenError(): never {
  throw new HttpsError("permission-denied", "Invalid bot token");
}

export function throwUserNotFoundError(): never {
  throw new HttpsError("not-found", "User not found");
}

export function throwOrderNotFoundError(): never {
  throw new HttpsError("not-found", "Order not found");
}

export function throwOrderDataCorruptedError(): never {
  throw new HttpsError("internal", "Order data is corrupted");
}

export function throwOrderNotCancelledError(): never {
  throw new HttpsError(
    "failed-precondition",
    "Order must be cancelled to reset gift"
  );
}

export function throwUserNotSellerError(): never {
  throw new HttpsError(
    "permission-denied",
    "Only the seller can reset the gift for this order"
  );
}

export function handleResetGiftServiceError(
  error: unknown,
  params: { userId: string; orderId: string }
): never {
  logResetGiftServiceError(error, params);

  // Re-throw HttpsError as-is
  if (error instanceof HttpsError) {
    throw error;
  }

  // Wrap other errors
  throw new HttpsError("internal", "Failed to reset gift on cancelled order");
}

export function handleResetGiftFunctionError(error: unknown) {
  if (error instanceof HttpsError) {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.message,
      },
    };
  }

  return {
    success: false,
    error: {
      code: "internal",
      message: "An internal error occurred",
    },
  };
}
