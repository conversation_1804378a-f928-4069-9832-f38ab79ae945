import { createServiceLogger } from "../logger/base-logger";

const resetGiftLogger = createServiceLogger("reset-gift-on-cancelled-order");

export function logResetGiftFunctionCalled(params: {
  userId?: string;
  orderId?: string;
  hasBotToken: boolean;
}) {
  resetGiftLogger.logInfo(
    "Reset gift on cancelled order function called",
    "reset_gift_on_cancelled_order",
    params
  );
}

export function logResetGiftServiceStarted(params: {
  userId: string;
  orderId: string;
}) {
  resetGiftLogger.logInfo(
    "Starting reset gift on cancelled order service",
    "reset_gift_service",
    params
  );
}

export function logBotTokenNotConfigured(params: {
  userId: string;
  orderId: string;
}) {
  resetGiftLogger.logError(
    "Bot token not configured in environment",
    new Error("Bot token not configured"),
    "reset_gift_service",
    params
  );
}

export function logInvalidBotToken(params: {
  userId: string;
  orderId: string;
}) {
  resetGiftLogger.logWarn(
    "Invalid bot token provided",
    "reset_gift_service",
    params
  );
}

export function logUserNotFound(params: { userId: string; orderId: string }) {
  resetGiftLogger.logWarn("User not found", "reset_gift_service", params);
}

export function logOrderNotFound(params: { userId: string; orderId: string }) {
  resetGiftLogger.logWarn("Order not found", "reset_gift_service", params);
}

export function logOrderDataCorrupted(params: {
  userId: string;
  orderId: string;
}) {
  resetGiftLogger.logError(
    "Order data is null",
    new Error("Order data is corrupted"),
    "reset_gift_service",
    params
  );
}

export function logOrderNotCancelled(params: {
  userId: string;
  orderId: string;
  orderStatus: string;
}) {
  resetGiftLogger.logWarn(
    "Order is not cancelled",
    "reset_gift_service",
    params
  );
}

export function logUserNotSeller(params: {
  userId: string;
  orderId: string;
  orderSellerId: string;
}) {
  resetGiftLogger.logWarn(
    "User is not the seller of the order",
    "reset_gift_service",
    params
  );
}

export function logGiftResetSuccess(params: {
  userId: string;
  orderId: string;
}) {
  resetGiftLogger.logInfo(
    "Gift field reset successfully",
    "reset_gift_service",
    params
  );
}

export function logResetGiftCompleted(params: {
  userId: string;
  orderId: string;
  success: boolean;
}) {
  resetGiftLogger.logInfo(
    "Reset gift on cancelled order completed successfully",
    "reset_gift_on_cancelled_order",
    params
  );
}

export function logResetGiftServiceError(
  error: unknown,
  params: {
    userId: string;
    orderId: string;
  }
) {
  resetGiftLogger.logError(
    "Error in reset gift service",
    error,
    "reset_gift_service",
    params
  );
}
