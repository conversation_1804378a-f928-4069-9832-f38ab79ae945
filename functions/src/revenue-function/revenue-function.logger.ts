import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const revenueLogger = createServiceLogger("revenue-function");

export function logRevenueWithdrawal({
  withdrawAmount,
  johnDowWallet,
  userId,
  transactionHash,
}: {
  withdrawAmount: number;
  johnDowWallet: string;
  userId: string;
  transactionHash?: string;
}) {
  revenueLogger.logInfo(
    `Revenue transferred: ${withdrawAmount.toFixed(
      4
    )} TON sent to John <PERSON> wallet`,
    "revenue_withdrawal",
    {
      withdrawAmount,
      johnDowWallet,
      userId,
      transactionHash,
    }
  );
}

export function logRevenueWithdrawalError({
  error,
  withdrawAmount,
  johnDowWallet,
  userId,
}: {
  error: unknown;
  withdrawAmount: number;
  johnDowWallet: string;
  userId?: string;
}) {
  revenueLogger.logError(
    "Error in withdrawRevenue function",
    error,
    "revenue_withdrawal",
    {
      withdrawAmount,
      johnDowWallet,
      userId,
    }
  );
}
