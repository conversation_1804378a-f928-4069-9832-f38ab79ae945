import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  MARKETPLACE_REVENUE_USER_ID,
  UserEntity,
} from "../mikerudenko/marketplace-shared";
import { MIN_REVENUE_BALANCE_ON_WALLET } from "../constants";
import { requireAdminRole } from "../services/auth-middleware";
import {
  hasAvailableBalance,
  updateUserBalance,
} from "../services/balance-service/balance-service";
import { sendRevenueTransfer } from "../services/ton-wallet-service/ton-wallet-service";
import { safeSubtract } from "../utils";
import {
  throwInvalidWithdrawAmount,
  throwInvalidWalletAddress,
  throwRevenueAccountNotFound,
  throwInsufficientRevenueBalance,
  throwExceedsMaxWithdrawal,
  throwInsufficientBalance,
} from "./revenue-function.error-handler";

export interface WithdrawRevenueParams {
  withdrawAmount: number;
  johnDowWallet: string;
}

export interface WithdrawRevenueResult {
  success: boolean;
  message: string;
  totalAmount: number;
  transactionHash?: string;
}

export function validateWithdrawRevenueParams(
  params: WithdrawRevenueParams
): void {
  const { withdrawAmount, johnDowWallet } = params;

  if (!withdrawAmount || withdrawAmount <= 0) {
    throwInvalidWithdrawAmount();
  }

  if (!johnDowWallet) {
    throwInvalidWalletAddress();
  }
}

export async function validateAdminAccess(userId: string): Promise<void> {
  await requireAdminRole(userId);
}

export async function getRevenueUser(): Promise<UserEntity> {
  const db = admin.firestore();
  const revenueDoc = await db
    .collection(APP_USERS_COLLECTION)
    .doc(MARKETPLACE_REVENUE_USER_ID)
    .get();

  if (!revenueDoc.exists) {
    throwRevenueAccountNotFound();
  }

  return {
    id: revenueDoc.id,
    ...revenueDoc.data(),
  } as UserEntity;
}

export function validateRevenueBalance(
  revenueUser: UserEntity,
  withdrawAmount: number
): void {
  const availableRevenue = revenueUser.balance
    ? safeSubtract(revenueUser.balance.sum, revenueUser.balance.locked)
    : 0;

  if (availableRevenue < MIN_REVENUE_BALANCE_ON_WALLET) {
    throwInsufficientRevenueBalance(MIN_REVENUE_BALANCE_ON_WALLET);
  }

  const maxWithdrawable = safeSubtract(
    availableRevenue,
    MIN_REVENUE_BALANCE_ON_WALLET
  );

  if (withdrawAmount > maxWithdrawable) {
    throwExceedsMaxWithdrawal(maxWithdrawable, MIN_REVENUE_BALANCE_ON_WALLET);
  }
}

export async function validateSufficientBalance(
  withdrawAmount: number
): Promise<void> {
  const hasBalance = await hasAvailableBalance(
    MARKETPLACE_REVENUE_USER_ID,
    withdrawAmount
  );
  if (!hasBalance) {
    throwInsufficientBalance();
  }
}

export async function processRevenueWithdrawal(
  withdrawAmount: number,
  johnDowWallet: string
): Promise<{ success: boolean; transactionHash?: string }> {
  await updateUserBalance({
    userId: MARKETPLACE_REVENUE_USER_ID,
    sumChange: -withdrawAmount,
    lockedChange: 0,
  });

  return await sendRevenueTransfer(withdrawAmount, johnDowWallet);
}
