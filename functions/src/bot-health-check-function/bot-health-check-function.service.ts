import fetch from "node-fetch";
import { getEnv } from "../config";
import { BOT_HEALTH_CHECK_ENDPOINT } from "../constants";
import {
  throwBotAppUrlNotConfigured,
  throwHealthCheckFailed,
} from "./bot-health-check-function.error-handler";
import {
  logHealthCheckCall,
  logHealthCheckFailed,
  logHealthCheckPassed,
  logHealthCheckResponse,
  logHealthCheckStarted,
  logHealthCheckUnhealthy,
} from "./bot-health-check-function.logger";

export interface HealthCheckResult {
  success: boolean;
  status: string;
  timestamp: string;
}

export async function performBotHealthCheck(): Promise<HealthCheckResult> {
  try {
    logHealthCheckStarted({ status: "started" });

    const botAppUrl = getEnv().url.webhook_url;

    if (!botAppUrl) {
      throwBotAppUrlNotConfigured();
    }

    const healthCheckUrl = `${botAppUrl}${BOT_HEALTH_CHECK_ENDPOINT}`;
    logHealthCheckCall({
      healthCheckUrl,
      hasAuthentication: true,
    });

    const response = await fetch(healthCheckUrl, {
      method: "GET",
      timeout: 30000, // 30 second timeout
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "marketplace-functions/health-check",
      },
    });

    if (!response.ok) {
      throwHealthCheckFailed(response.status, response.statusText);
    }

    const responseData = await response.json();
    logHealthCheckResponse({ responseData });

    if (responseData.status === "healthy") {
      logHealthCheckPassed({ status: "healthy" });
    } else {
      logHealthCheckUnhealthy({
        status: "unhealthy",
        responseData,
      });
    }

    return {
      success: true,
      status: responseData.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logHealthCheckFailed({
      error,
      status: "failed",
    });
    throw error;
  }
}
