import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const botHealthCheckLogger = createServiceLogger("bot-health-check-function");

export function logHealthCheckStarted({ status }: { status: string }) {
  botHealthCheckLogger.logInfo("Bot health check started", "health_check", {
    status,
  });
}

export function logHealthCheckCall({
  healthCheckUrl,
  hasAuthentication,
}: {
  healthCheckUrl: string;
  hasAuthentication: boolean;
}) {
  botHealthCheckLogger.logInfo("Making health check call", "health_check", {
    healthCheckUrl,
    hasAuthentication,
  });
}

export function logHealthCheckResponse({
  responseData,
}: {
  responseData: any;
}) {
  botHealthCheckLogger.logInfo(
    "Health check response received",
    "health_check",
    { responseData }
  );
}

export function logHealthCheckPassed({ status }: { status: string }) {
  botHealthCheckLogger.logInfo("Bot health check passed", "health_check", {
    status,
  });
}

export function logHealthCheckUnhealthy({
  status,
  responseData,
}: {
  status: string;
  responseData: any;
}) {
  botHealthCheckLogger.logInfo("Bot health check unhealthy", "health_check", {
    status,
    responseData,
  });
}

export function logHealthCheckFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  botHealthCheckLogger.logError(
    "Bot health check failed",
    error,
    "health_check",
    {
      status,
    }
  );
}

export function logMonitorTriggered({
  status,
  timestamp,
}: {
  status: string;
  timestamp: string;
}) {
  botHealthCheckLogger.logInfo("Bot health monitor triggered", "monitor", {
    status,
    timestamp,
  });
}

export function logMonitorCompleted() {
  botHealthCheckLogger.logInfo("Bot health monitor completed", "monitor", {});
}

export function logMonitorFailed({
  error,
  status,
}: {
  error: unknown;
  status: string;
}) {
  botHealthCheckLogger.logError("Bot health monitor failed", error, "monitor", {
    status,
  });
}
