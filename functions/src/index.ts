import * as admin from "firebase-admin";
import { getEnv } from "./config";
import { log } from "./logger/logger";

if (!admin.apps.length) {
  const config = getEnv();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with service account key", {
      operation: "firebase_init",
      method: "service_account",
    });
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    log.info("Firebase initialized with application default credentials", {
      operation: "firebase_init",
      method: "application_default",
    });
  }
}

admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

// Feature-based monitor functions
export { tonMonitor as tonTransactionMonitor } from "./ton-monitor-function";
export { expiredOrdersMonitor } from "./expired-orders-monitor-function";
export { limitedCollectionsMonitor } from "./limited-collections-monitor-function";
export { botHealthCheck } from "./bot-health-check-function";

// Feature-based utility functions
export { getTxLookup, updateTxLookup } from "./tx-lookup-function";

// New feature-based exports
export {
  getOrderByIdByBot,
  getUserOrdersByBot,
  sendGiftToRelayerByBot,
  depositGiftDirectlyByBot,
} from "./order-functions/bot-order-function";

export { getGiftByIdByBot } from "./gift-functions/get-gift-by-id-function/get-gift-by-id-function";

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order-function";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order-function";

export { withdrawFunds } from "./withdraw-function";

export { withdrawRevenue } from "./revenue-function";

export { signInWithTelegram } from "./telegram-auth-function";

export { changeUserData } from "./user-profile-function";

export {
  clearUserSessionByBot,
  getUserSessionByBot,
  saveUserSessionByBot,
} from "./bot-session-function";

// Order functions from order-functions-new
export { cancelUserOrder } from "./order-functions/general-order-function";
export { fulfillOrderAndCreateResellOrder } from "./order-functions/fulfill-and-resell-function";
export {
  makeSecondaryMarketPurchase,
  setSecondaryMarketPrice,
} from "./order-functions/secondary-market-function";

export {
  clearDeadlines as clearOrderDeadlines,
  recalculateDeadlines as recalculateOrderDeadlines,
} from "./admin-collection-function";

export { setGiftAsWithdrawnOnCancelledOrderByBot } from "./reset-gift-on-cancelled-order-function/reset-gift-on-cancelled-order";

export { linkGiftToOrder } from "./link-gift-to-order-function/link-gift-to-order";

export { createSellOrderFromGift } from "./create-sell-order-from-gift-function/create-sell-order-from-gift";

export { getUserGiftsAvailableForWithdrawalByBot } from "./gift-functions/get-user-gifts-available-for-withdrawal-function/get-user-gifts-available-for-withdrawal-function";
export { withdrawGiftByBot } from "./gift-functions/withdraw-gift-function/withdraw-gift-function";
