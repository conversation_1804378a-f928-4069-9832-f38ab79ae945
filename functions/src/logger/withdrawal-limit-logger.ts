import { createServiceLogger } from "./base-logger";

// Create service logger using functional composition
const withdrawalLimitLogger = createServiceLogger("withdrawal-limit");

export function logLimitCheckCompleted({
  userId,
  requestedAmount,
  maxWithdrawalAmount,
  currentWithdrawn,
  remainingLimit,
  canWithdraw,
}: {
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
  currentWithdrawn: number;
  remainingLimit: number;
  canWithdraw: boolean;
}) {
  withdrawalLimitLogger.logInfo(
    "Withdrawal limit check completed",
    "check_withdrawal_limit",
    {
      userId,
      requestedAmount,
      maxWithdrawalAmount,
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
    }
  );
}

export function logLimitCheckError({
  error,
  userId,
  requestedAmount,
  maxWithdrawalAmount,
}: {
  error: unknown;
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
}) {
  withdrawalLimitLogger.logError(
    "Error checking withdrawal limit",
    error,
    "check_withdrawal_limit",
    {
      userId,
      requestedAmount,
      maxWithdrawalAmount,
    }
  );
}

export function logTrackingUpdated({
  userId,
  withdrawnAmount,
  newWithdrawnAmount,
  resetTime,
}: {
  userId: string;
  withdrawnAmount: number;
  newWithdrawnAmount: number;
  resetTime: Date;
}) {
  withdrawalLimitLogger.logInfo(
    "Withdrawal tracking updated",
    "update_withdrawal_tracking",
    {
      userId,
      withdrawnAmount,
      newWithdrawnAmount,
      resetTime,
    }
  );
}

export function logTrackingUpdateError({
  error,
  userId,
  withdrawnAmount,
}: {
  error: unknown;
  userId: string;
  withdrawnAmount: number;
}) {
  withdrawalLimitLogger.logError(
    "Error updating withdrawal tracking",
    error,
    "update_withdrawal_tracking",
    {
      userId,
      withdrawnAmount,
    }
  );
}
