import { createServiceLogger } from "./base-logger";

// Create service loggers using functional composition
const buyerOrderLogger = createServiceLogger("buyer-order");
const sellerOrderLogger = createServiceLogger("seller-order");
const generalOrderLogger = createServiceLogger("general-order");
const botOrderLogger = createServiceLogger("bot-order");
const fulfillAndResellLogger = createServiceLogger("fulfill-and-resell");
const transactionHistoryLogger = createServiceLogger("transaction-history");
const feeServiceLogger = createServiceLogger("fee-service");

// Buyer Order Logger Functions
export function logBuyerCreateOrderError({
  error,
  requestData,
  userId,
}: {
  error: unknown;
  requestData?: any;
  userId?: string;
}) {
  buyerOrderLogger.logError(
    "Error creating order as buyer",
    error,
    "create_order_as_buyer",
    {
      requestData,
      userId,
    }
  );
}

export function logBuyerPurchaseError({
  error,
  buyerId,
  orderId,
}: {
  error: unknown;
  buyerId?: string;
  orderId?: string;
}) {
  buyerOrderLogger.logError(
    "Error making purchase as buyer",
    error,
    "buyer_purchase",
    {
      buyerId,
      orderId,
    }
  );
}

// Seller Order Logger Functions
export function logSellerCreateOrderError({
  error,
  requestData,
  sellerId,
}: {
  error: unknown;
  requestData?: any;
  sellerId?: string;
}) {
  sellerOrderLogger.logError(
    "Error creating order as seller",
    error,
    "create_order_as_seller",
    {
      sellerId,
      collectionId: requestData?.collectionId,
      price: requestData?.price,
    }
  );
}

export function logSellerPurchaseError({
  error,
  requestData,
  userId,
}: {
  error: unknown;
  requestData?: any;
  userId?: string;
}) {
  sellerOrderLogger.logError(
    "Error making purchase as seller",
    error,
    "purchase_as_seller",
    {
      requestData,
      userId,
    }
  );
}

// General Order Logger Functions
export function logCancelOrderRequest({
  orderId,
  userId,
  environment,
}: {
  orderId: string;
  userId: string;
  environment?: string;
}) {
  generalOrderLogger.logDebug("Cancel order request received", "cancel_order", {
    orderId,
    userId,
    environment,
  });
}

export function logCancelOrderError({
  error,
  orderId,
  userId,
}: {
  error: unknown;
  orderId?: string;
  userId?: string;
}) {
  generalOrderLogger.logError(
    "Error cancelling order",
    error,
    "cancel_user_order",
    {
      orderId,
      userId,
    }
  );
}

// Bot Order Logger Functions
export function logGetOrderError(data: {
  error: unknown;
  orderId: string;
}): void {
  botOrderLogger.logError(
    "Error getting order by ID",
    data.error,
    "get_order_by_id",
    {
      orderId: data.orderId,
    }
  );
}

export function logGetUserOrdersError(data: {
  error: unknown;
  userId?: string;
}): void {
  botOrderLogger.logError(
    "Error getting user orders",
    data.error,
    "get_user_orders_by_bot",
    {
      userId: data.userId,
    }
  );
}

export function logSendGiftToRelayerError(data: {
  error: unknown;
  orderId: string;
}): void {
  botOrderLogger.logError(
    "Error sending gift to relayer",
    data.error,
    "send_gift_to_relayer_by_bot",
    {
      orderId: data.orderId,
    }
  );
}

export function logCompletePurchaseError(data: {
  error: unknown;
  orderId: string;
}): void {
  botOrderLogger.logError(
    "Error completing purchase",
    data.error,
    "complete_purchase_by_bot",
    {
      orderId: data.orderId,
    }
  );
}

// Fulfill and Resell Logger Functions
export function logOrderFulfilledAndResellCreated({
  originalOrderId,
  newOrderId,
  userId,
  price,
}: {
  originalOrderId: string;
  newOrderId: string;
  userId: string;
  price: number;
}) {
  fulfillAndResellLogger.logInfo(
    "Order fulfilled and resell order created",
    "fulfill_and_resell",
    {
      originalOrderId,
      newOrderId,
      userId,
      price,
    }
  );
}

export function logFulfillAndResellError({
  error,
  requestData,
  userId,
}: {
  error: unknown;
  requestData?: any;
  userId?: string;
}) {
  fulfillAndResellLogger.logError(
    "Error fulfilling order and creating resell order",
    error,
    "fulfill_and_resell",
    {
      requestData,
      userId,
    }
  );
}

// Transaction History Logger Functions
export function logTransactionRecordCreated({
  userId,
  txType,
  originalAmount,
  signedAmount,
  orderId,
}: {
  userId: string;
  txType: string;
  originalAmount: number;
  signedAmount: number;
  orderId?: string;
}) {
  transactionHistoryLogger.logInfo(
    "Transaction history record created",
    "create_transaction_record",
    {
      userId,
      txType,
      originalAmount,
      signedAmount,
      orderId,
    }
  );
}

// Fee Service Logger Functions
export function logAppConfigNotFound() {
  feeServiceLogger.logWarn(
    "App config not found, using zero fees",
    "app_config_fetch",
    {}
  );
}
