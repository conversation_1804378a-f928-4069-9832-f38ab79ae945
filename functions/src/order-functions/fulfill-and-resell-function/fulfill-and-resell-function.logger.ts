import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const fulfillAndResellLogger = createServiceLogger("fulfill-and-resell-function");

export function logFulfillAndResellStarted({
  orderId,
  userId,
  price,
}: {
  orderId: string;
  userId: string;
  price: number;
}) {
  fulfillAndResellLogger.logInfo(
    "Fulfill and resell order started",
    "fulfill_and_resell",
    {
      orderId,
      userId,
      price,
    }
  );
}

export function logFulfillAndResellSuccess({
  originalOrderId,
  newOrderId,
  userId,
  price,
  lockAmount,
}: {
  originalOrderId: string;
  newOrderId: string;
  userId: string;
  price: number;
  lockAmount: number;
}) {
  fulfillAndResellLogger.logInfo(
    "Order fulfilled and resell order created successfully",
    "fulfill_and_resell",
    {
      originalOrderId,
      newOrderId,
      userId,
      price,
      lockAmount,
    }
  );
}

export function logFulfillAndResellError({
  error,
  orderId,
  userId,
  price,
}: {
  error: unknown;
  orderId?: string;
  userId?: string;
  price?: number;
}) {
  fulfillAndResellLogger.logError(
    "Error fulfilling order and creating resell order",
    error,
    "fulfill_and_resell",
    {
      orderId,
      userId,
      price,
    }
  );
}
