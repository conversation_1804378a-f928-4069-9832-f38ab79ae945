import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
  getUserData,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { validateSellerCreatedOrdersLimit } from "../../services/order-validation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";
import { validateGiftForLinking } from "../../services/gift-validation.service";
import { getUserById } from "../../services/user-lookup.service";

export interface CreateOrderAsSellerParams {
  sellerId: string;
  collectionId: string;
  price: number;
  giftId?: string;
}

export interface MakePurchaseAsSellerParams {
  sellerId: string;
  orderId: string;
}

export class SellerOrderFunctionService {
  static async validateCreateOrderRequest(
    request: any,
    params: CreateOrderAsSellerParams
  ) {
    const authRequest = requireAuthentication(request);
    validateOrderCreationParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);

    // Validate user has telegram ID for order operations
    const user = await getUserData(authRequest.auth.uid);
    validateTelegramIdForOrderOperation(user, "create orders");

    return authRequest;
  }

  static async validateGiftForOrder(
    giftId: string,
    sellerId: string,
    collectionId: string
  ): Promise<void> {
    // 1. Validate gift (existence, status, not used in other orders)
    const gift = await validateGiftForLinking(giftId);

    // 2. Get user to validate gift ownership
    const user = await getUserById(sellerId);
    if (!user || user.tg_id !== gift.owner_tg_id) {
      throw new Error("Gift does not belong to the seller");
    }

    // 3. Validate gift and order are from the same collection
    if (gift.collectionId !== collectionId) {
      throw new Error("Gift collection does not match order collection");
    }
  }

  static async validatePurchaseRequest(
    request: any,
    params: MakePurchaseAsSellerParams
  ) {
    const authRequest = requireAuthentication(request);
    validatePurchaseParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);

    // Validate user has telegram ID for purchase operations
    const user = await getUserData(authRequest.auth.uid);
    validateTelegramIdForOrderOperation(user, "make purchases");

    return authRequest;
  }

  static async createSellerOrder(
    params: CreateOrderAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, collectionId, price, giftId } = params;

    // If giftId is provided, validate the gift
    if (giftId) {
      await SellerOrderFunctionService.validateGiftForOrder(
        giftId,
        sellerId,
        collectionId
      );
    }

    // Validate that seller doesn't have too many orders with "created" status
    await validateSellerCreatedOrdersLimit(db, sellerId, collectionId);

    return await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      giftId: giftId || null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });
  }

  static async processSellerPurchase(
    params: MakePurchaseAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, orderId } = params;

    return await processPurchase(db, {
      userId: sellerId,
      orderId,
      userType: UserType.SELLER,
    });
  }
}
