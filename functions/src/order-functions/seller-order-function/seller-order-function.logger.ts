import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const sellerOrderLogger = createServiceLogger("seller-order-function");

export function logCreateOrderError({
  error,
  operation,
  requestData,
  sellerId,
}: {
  error: unknown;
  operation: string;
  requestData?: any;
  sellerId?: string;
}) {
  sellerOrderLogger.logError("Error creating seller order", error, operation, {
    sellerId,
    requestData,
  });
}

export function logPurchaseError({
  error,
  operation,
  requestData,
  userId,
}: {
  error: unknown;
  operation: string;
  requestData?: any;
  userId?: string;
}) {
  sellerOrderLogger.logError("Error in seller purchase", error, operation, {
    requestData,
    userId,
  });
}
