import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const generalOrderLogger = createServiceLogger("general-order-function");

export function logCancelOrderStarted({
  orderId,
  userId,
}: {
  orderId: string;
  userId: string;
}) {
  generalOrderLogger.logInfo("Order cancellation started", "cancel_order", {
    orderId,
    userId,
  });
}

export function logCancelOrderSuccess({
  orderId,
  userId,
  feeApplied,
  feeType,
}: {
  orderId: string;
  userId: string;
  feeApplied: number;
  feeType: string;
}) {
  generalOrderLogger.logInfo("Order cancelled successfully", "cancel_order", {
    orderId,
    userId,
    feeApplied,
    feeType,
  });
}

export function logCancelOrderError({
  error,
  orderId,
  userId,
}: {
  error: unknown;
  orderId?: string;
  userId?: string;
}) {
  generalOrderLogger.logError("Error cancelling order", error, "cancel_order", {
    orderId,
    userId,
  });
}
