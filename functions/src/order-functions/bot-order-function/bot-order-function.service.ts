import * as admin from "firebase-admin";
import {
  AppDate,
  GiftFromBot,
  GiftStatus,
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
  formatDateToFirebaseTimestamp,
} from "../../mikerudenko/marketplace-shared";
import { validateBotTokenSimple } from "../../services/bot-validation.service";
import { createGift, updateGiftOwnership } from "../../services/gift-service";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwInvalidOrderId,
  throwInvalidOrderStatus,
  throwOrderNotFound,
} from "./bot-order-function.error-handler";
import { getUserById } from "../../services/user-lookup.service";

async function handleDirectGiftDepositFlow(
  gift: GiftFromBot,
  userTgId: string,
  collectionId?: string
) {
  // Create gift in separate collection without order association
  const giftId = await createGift(gift, userTgId, collectionId || "");

  return {
    success: true,
    message: "Gift deposited successfully.",
    giftId,
  };
}

export function validateBotToken(botToken?: string): void {
  try {
    validateBotTokenSimple(botToken);
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Bot token is required")
    ) {
      throwBotTokenRequired();
    } else {
      throwInvalidBotToken();
    }
  }
}

export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throwInvalidOrderId();
  }
}

export async function completePurchaseForBot(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  await orderDoc.ref.update({
    status: OrderStatus.FULFILLED,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  // Transfer gift ownership to buyer when order becomes fulfilled
  if (order.giftId && order.buyerId) {
    try {
      const buyerTgId = (await getUserById(order.buyerId))?.tg_id;
      if (!buyerTgId) {
        throw new Error("Buyer telegram ID not found");
      }
      await updateGiftOwnership(order.giftId, buyerTgId, GiftStatus.WITHDRAWN);
    } catch (error) {
      console.warn("Failed to transfer gift ownership:", error);
    }
  }

  return {
    success: true,
    message: "Purchase completed successfully.",
    order: {
      ...order,
      status: OrderStatus.FULFILLED,
    },
  };
}

export async function depositGiftDirectlyForBot(params: {
  gift: GiftFromBot;
  userTgId: string;
  botToken: string;
  collectionId?: string;
}) {
  const { gift, userTgId, botToken, collectionId } = params;

  validateBotToken(botToken);

  if (!userTgId) {
    throw new Error("User telegram ID is required");
  }

  return await handleDirectGiftDepositFlow(gift, userTgId, collectionId);
}
