import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const botOrderLogger = createServiceLogger("bot-order-function");

export function logGetOrderError({
  error,
  orderId,
}: {
  error: unknown;
  orderId?: string;
}) {
  botOrderLogger.logError(
    "Error getting order by bot",
    error,
    "get_order_by_bot",
    {
      orderId,
    }
  );
}

export function logGetUserOrdersError({
  error,
  userId,
}: {
  error: unknown;
  userId?: string;
}) {
  botOrderLogger.logError(
    "Error getting user orders by bot",
    error,
    "get_user_orders_by_bot",
    {
      userId,
    }
  );
}

export function logCompletePurchaseError({
  error,
  orderId,
}: {
  error: unknown;
  orderId?: string;
}) {
  botOrderLogger.logError(
    "Error completing purchase by bot",
    error,
    "complete_purchase_by_bot",
    {
      orderId,
    }
  );
}

export function logSendGiftError({
  error,
  orderId,
}: {
  error: unknown;
  orderId?: string;
}) {
  botOrderLogger.logError(
    "Error sending gift to relayer by bot",
    error,
    "send_gift_to_relayer_by_bot",
    {
      orderId,
    }
  );
}

export function logOrderFulfilled({
  orderId,
  buyerId,
  sellerId,
  amount,
}: {
  orderId: string;
  buyerId: string;
  sellerId: string;
  amount: number;
}) {
  botOrderLogger.logInfo(
    `Order ${orderId} fulfilled successfully`,
    "order_fulfillment",
    {
      orderId,
      buyerId,
      sellerId,
      amount,
    }
  );
}

export function logDebugInfo({
  orderId,
  operation,
  details,
}: {
  orderId: string;
  operation: string;
  details: any;
}) {
  botOrderLogger.logDebug(`Bot order operation: ${operation}`, operation, {
    orderId,
    details,
  });
}
