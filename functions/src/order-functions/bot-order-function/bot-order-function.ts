import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import { GiftFromBot } from "../../mikerudenko/marketplace-shared";
import {
  getOrderByIdForBot,
  getUserOrdersForBot,
} from "../../services/order-query.service";
import { throwBotOrderInternalError } from "./bot-order-function.error-handler";
import {
  logGetOrderError,
  logGetUserOrdersError,
  logSendGiftError,
} from "./bot-order-function.logger";
import { depositGiftDirectlyForBot } from "./bot-order-function.service";

export const getOrderByIdByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  try {
    return await getOrderByIdForBot({ orderId, botToken });
  } catch (error) {
    logGetOrderError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const getUserOrdersByBot = onCall<{
  userId?: string;
  tgId?: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, tgId, botToken } = request.data;

  try {
    return await getUserOrdersForBot({ userId, tgId, botToken });
  } catch (error) {
    logGetUserOrdersError({
      error,
      userId: request.data.userId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const depositGiftDirectlyByBot = onCall<{
  gift: GiftFromBot;
  userTgId: string;
  botToken: string;
  collectionId?: string;
}>(commonFunctionsConfig, async (request) => {
  const { gift, userTgId, botToken, collectionId } = request.data;

  try {
    return await depositGiftDirectlyForBot({
      gift,
      userTgId,
      botToken,
      collectionId,
    });
  } catch (error) {
    logSendGiftError({
      error,
      orderId: "direct_deposit",
    });
    throwBotOrderInternalError((error as any).message);
  }
});
