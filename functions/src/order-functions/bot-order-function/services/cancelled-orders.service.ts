import {
  OrderEntity,
  GiftStatus,
} from "../../../mikerudenko/marketplace-shared";
import { getGiftById } from "../../../services/gift-service";

export async function processCancelledOrdersWithGifts(
  cancelledOrders: OrderEntity[]
): Promise<OrderEntity[]> {
  const cancelledOrdersWithGifts: OrderEntity[] = [];

  for (const order of cancelledOrders) {
    if (order.giftId && !order.buyerId) {
      try {
        const gift = await getGiftById(order.giftId);

        if (gift && gift.status === GiftStatus.DEPOSITED) {
          cancelledOrdersWithGifts.push(order);
        }
      } catch (error) {
        console.error(
          `Error fetching gift ${order.giftId} for order ${order.id}:`,
          error
        );
      }
    }
  }

  return cancelledOrdersWithGifts;
}
