import { HttpsError } from "firebase-functions/v2/https";
import {
  GENERIC_ERRORS,
  ORDER_ERRORS,
  VALIDATION_ERRORS,
} from "../../error-messages";

export function throwInvalidOrderId(): never {
  throw new HttpsError(
    "invalid-argument",
    JSON.stringify({
      errorKey: VALIDATION_ERRORS.INVALID_ORDER_ID,
      fallbackMessage: "Order ID is required.",
    })
  );
}

export function throwBotTokenRequired(): never {
  throw new HttpsError(
    "invalid-argument",
    JSON.stringify({
      errorKey: VALIDATION_ERRORS.BOT_TOKEN_REQUIRED,
      fallbackMessage: "Bot token is required.",
    })
  );
}

export function throwInvalidBotToken(): never {
  throw new HttpsError(
    "permission-denied",
    JSON.stringify({
      errorKey: VALIDATION_ERRORS.INVALID_BOT_TOKEN,
      fallbackMessage: "Invalid bot token.",
    })
  );
}

export function throwUserIdOrTgIdRequired(): never {
  throw new HttpsError(
    "invalid-argument",
    JSON.stringify({
      errorKey: VALIDATION_ERRORS.USER_ID_OR_TG_ID_REQUIRED,
      fallbackMessage: "Either userId or tgId is required.",
    })
  );
}

export function throwOrderNotFound(): never {
  throw new HttpsError(
    "not-found",
    JSON.stringify({
      errorKey: ORDER_ERRORS.ORDER_NOT_FOUND,
      fallbackMessage: "Order not found.",
    })
  );
}

export function throwInvalidOrderStatus(): never {
  throw new HttpsError(
    "failed-precondition",
    JSON.stringify({
      errorKey: ORDER_ERRORS.INVALID_ORDER_STATUS,
      fallbackMessage: "Order status is not valid for this operation.",
    })
  );
}

export function throwBotOrderInternalError(message?: string): never {
  throw new HttpsError(
    "internal",
    JSON.stringify({
      errorKey: GENERIC_ERRORS.SERVER_ERROR,
      fallbackMessage: message ?? "Server error while processing request.",
    })
  );
}
