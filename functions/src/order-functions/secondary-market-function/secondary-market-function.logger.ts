import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const secondaryMarketLogger = createServiceLogger("secondary-market-function");

export function logSetSecondaryPriceStarted({
  orderId,
  userId,
  secondaryMarketPrice,
}: {
  orderId: string;
  userId: string;
  secondaryMarketPrice: number;
}) {
  secondaryMarketLogger.logInfo(
    "Set secondary market price started",
    "set_secondary_price",
    {
      orderId,
      userId,
      secondaryMarketPrice,
    }
  );
}

export function logSetSecondaryPriceSuccess({
  orderId,
  userId,
  secondaryMarketPrice,
}: {
  orderId: string;
  userId: string;
  secondaryMarketPrice: number;
}) {
  secondaryMarketLogger.logInfo(
    "Secondary market price set successfully",
    "set_secondary_price",
    {
      orderId,
      userId,
      secondaryMarketPrice,
    }
  );
}

export function logSecondaryPurchaseStarted({
  orderId,
  newBuyerId,
  oldBuyerId,
  secondaryMarketPrice,
}: {
  orderId: string;
  newBuyerId: string;
  oldBuyerId: string;
  secondaryMarketPrice: number;
}) {
  secondaryMarketLogger.logInfo(
    "Secondary market purchase started",
    "secondary_purchase",
    {
      orderId,
      newBuyerId,
      oldBuyerId,
      secondaryMarketPrice,
    }
  );
}

export function logSecondaryPurchaseSuccess({
  orderId,
  newBuyerId,
  oldBuyerId,
  secondaryMarketPrice,
  feeAmount,
  lockedAmount,
}: {
  orderId: string;
  newBuyerId: string;
  oldBuyerId: string;
  secondaryMarketPrice: number;
  feeAmount: number;
  lockedAmount: number;
}) {
  secondaryMarketLogger.logInfo(
    "Secondary market purchase completed successfully",
    "secondary_purchase",
    {
      orderId,
      newBuyerId,
      oldBuyerId,
      secondaryMarketPrice,
      feeAmount,
      lockedAmount,
    }
  );
}

export function logSecondaryMarketError({
  error,
  operation,
  orderId,
  userId,
}: {
  error: unknown;
  operation: string;
  orderId?: string;
  userId?: string;
}) {
  secondaryMarketLogger.logError(
    `Error in secondary market operation: ${operation}`,
    error,
    operation,
    {
      orderId,
      userId,
    }
  );
}
