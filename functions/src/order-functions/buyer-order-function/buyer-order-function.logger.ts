import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const buyerOrderLogger = createServiceLogger("buyer-order-function");

export function logCreateOrderError({
  error,
  operation,
  requestData,
  userId,
}: {
  error: unknown;
  operation: string;
  requestData?: any;
  userId?: string;
}) {
  buyerOrderLogger.logError("Error creating buyer order", error, operation, {
    requestData,
    userId,
  });
}

export function logPurchaseError({
  error,
  operation,
  buyerId,
  orderId,
}: {
  error: unknown;
  operation: string;
  buyerId?: string;
  orderId?: string;
}) {
  buyerOrderLogger.logError("Error in buyer purchase", error, operation, {
    buyerId,
    orderId,
  });
}
