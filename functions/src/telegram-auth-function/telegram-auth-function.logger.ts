import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const telegramAuthLogger = createServiceLogger("telegram-auth-function");

export function logAuthStarted(): void {
  telegramAuthLogger.logInfo(
    "Starting Telegram authentication",
    "telegram_signin",
    {}
  );
}

export function logValidationResult(data: {
  isValid: boolean;
  requestData: any;
}) {
  telegramAuthLogger.logInfo(
    "Telegram data validation result",
    "telegram_auth",
    {
      isValid: data.isValid,
      requestData: data.requestData,
    }
  );
}

export function logUserCreated(data: { userId: string; telegramId: string }) {
  telegramAuthLogger.logInfo(
    `New Telegram user created: ${data.userId}`,
    "telegram_signin",
    {
      userId: data.userId,
      action: "user_created",
    }
  );
}

export function logAuthError(data: { error: unknown; requestData: any }) {
  telegramAuthLogger.logError(
    "Telegram authentication error",
    data.error,
    "telegram_signin",
    {
      requestData: data.requestData,
    }
  );
}

export function logDevelopmentMode() {
  telegramAuthLogger.logInfo(
    "Using development mode with mock data",
    "telegram_auth",
    { mode: "development" }
  );
}

export function logMockUserParsed(data: {
  telegramId: string;
  username?: string;
  firstName?: string;
}) {
  telegramAuthLogger.logInfo("Mock user data parsed", "telegram_auth", data);
}

export function logValidationStarted() {
  telegramAuthLogger.logInfo("Validating Telegram data", "telegram_auth", {
    mode: "production",
  });
}

export function logFirebaseUserFound(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Found existing Firebase Auth user",
    "firebase_auth",
    data
  );
}

export function logFirebaseUserCreating(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Creating new Firebase Auth user",
    "firebase_auth",
    data
  );
}

export function logFirebaseUserCreated(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Firebase Auth user created successfully",
    "firebase_auth",
    data
  );
}

export function logCustomClaimsSet(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Custom claims set successfully",
    "firebase_auth",
    data
  );
}

export function logAppConfigLoaded(data: { config: any }) {
  telegramAuthLogger.logInfo("App config loaded", "firebase_auth", data);
}

export function logCustomTokenCreated(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Custom token created successfully",
    "firebase_auth",
    data
  );
}

export function logFirebaseAuthError(data: { error: unknown; userId: string }) {
  telegramAuthLogger.logError(
    "Firebase Auth user creation error",
    data.error,
    "firebase_auth",
    { userId: data.userId }
  );
}
