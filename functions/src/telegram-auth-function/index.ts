export { signInWithTelegram } from "./telegram-auth-function";
export {
  logAuthStarted,
  logValidationR<PERSON>ult,
  logUserCreated,
  logAuthError,
} from "./telegram-auth-function.logger";
export {
  validateTelegramData,
  findOrCreateUser,
  createFirebaseAuthUser,
} from "./telegram-auth-function.service";
export {
  throwInitDataRequired,
  throwInvalidTelegramData,
  throwFirebaseAuthError,
} from "./telegram-auth-function.error-handler";

export type {
  TelegramUser,
  ValidationResult,
  SignInWithTelegramParams,
  SignInWithTelegramResult,
} from "./telegram-auth-function.service";
