import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { validateBotRequest } from "../services/bot-validation-service";
import { commonFunctionsConfig } from "../constants";
import { AppDate, BotSessionEntity } from "../mikerudenko/marketplace-shared";
import { createServiceLogger } from "../logger/base-logger";

const db = admin.firestore();

const logger = createServiceLogger("bot-session-function");

function logSessionSaved({
  userId,
  sessionData,
}: {
  userId: string;
  sessionData: any;
}) {
  logger.logInfo("Bot session saved", "save_session", { userId, sessionData });
}

function logSessionSaveError({
  error,
  userId,
}: {
  error: unknown;
  userId: string;
}) {
  logger.logError("Error saving bot session", error, "save_session", {
    userId,
  });
}

function logSessionRetrieved({ userId }: { userId: string }) {
  logger.logInfo("Bot session retrieved", "get_session", { userId });
}

function logSessionCleared({ userId }: { userId: string }) {
  logger.logInfo("Bot session cleared", "clear_session", { userId });
}

export const saveUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
  sessionData: {
    pendingOrderId?: string;
    echoMode?: boolean;
    withdrawal_gift_id?: string;
  };
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken, sessionData } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    const existingSession = await sessionDoc.get();

    const sessionEntity: Partial<BotSessionEntity> = {
      id: userId,
      pendingOrderId: sessionData.pendingOrderId,
      echoMode: sessionData.echoMode,
      withdrawal_gift_id: sessionData.withdrawal_gift_id,
      createdAt: existingSession.exists
        ? existingSession.data()?.createdAt
        : (admin.firestore.FieldValue.serverTimestamp() as AppDate),
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    };

    await sessionDoc.set(sessionEntity, { merge: true });

    logSessionSaved({ userId, sessionData });

    return {
      success: true,
      message: "Session saved successfully",
    };
  } catch (error) {
    logSessionSaveError({ error, userId });
    throw new HttpsError("internal", "Failed to save session");
  }
});

export const getUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    const sessionDoc = await db.collection("bot_sessions").doc(userId).get();

    logSessionRetrieved({ userId });

    if (!sessionDoc.exists) {
      return {
        success: true,
        session: null,
        message: "No session found",
      };
    }

    return {
      success: true,
      session: { id: sessionDoc.id, ...sessionDoc.data() },
      message: "Session retrieved successfully",
    };
  } catch {
    throw new HttpsError("internal", "Failed to retrieve session");
  }
});

export const clearUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  validateBotRequest({ userId, botToken });

  try {
    await db.collection("bot_sessions").doc(userId).delete();

    logSessionCleared({ userId });

    return {
      success: true,
      message: "Session cleared successfully",
    };
  } catch {
    throw new HttpsError("internal", "Failed to clear session");
  }
});
