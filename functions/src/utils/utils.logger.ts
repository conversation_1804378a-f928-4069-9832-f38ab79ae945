import { createServiceLogger } from "../logger/base-logger";

// Create service logger using functional composition
const utilsLogger = createServiceLogger("utils");

export function logDivisionByZeroError({
  amount,
  divisor,
}: {
  amount: number;
  divisor: number;
}) {
  utilsLogger.logError(
    "Division by zero error in safeDivide function",
    new Error("Division by zero"),
    "safe_divide",
    {
      amount,
      divisor,
    }
  );
}
