import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const feeServiceLogger = createServiceLogger("fee-service");

export function logAppConfigNotFound() {
  feeServiceLogger.logInfo(
    "App config not found, using zero fees",
    "app_config_fetch",
    {}
  );
}

export function logAdminUserNotFound() {
  feeServiceLogger.logInfo("No admin user found", "admin_user_lookup", {});
}

export function logFeeApplied({
  feeAmount,
  feeType,
  userId,
  netAmount,
}: {
  feeAmount: number;
  feeType: string;
  userId?: string;
  netAmount?: number;
}) {
  feeServiceLogger.logInfo(`${feeType} fee applied`, "fee_applied", {
    feeAmount,
    feeType,
    userId,
    netAmount,
  });
}

export function logReferralFeeApplied({
  feeAmount,
  feeType,
  referrerFeeRate,
  referrerId,
  referralId,
}: {
  feeAmount: number;
  feeType: string;
  referrerFeeRate: number;
  referrerId: string;
  referralId: string;
}) {
  feeServiceLogger.logInfo("Purchase referral fee applied", "referral_fee", {
    feeAmount,
    feeType,
    referrerFeeRate,
    referrerId,
    referralId,
  });
}

export function logCustomReferralFee({
  referrerFeeRate,
  referrerId,
}: {
  referrerFeeRate: number;
  referrerId: string;
}) {
  feeServiceLogger.logInfo("Using custom referral fee", "custom_referral", {
    referrerFeeRate,
    referrerId,
  });
}

export function logOrderReferralFee({
  referrerFeeRate,
  referrerId,
}: {
  referrerFeeRate: number;
  referrerId: string;
}) {
  feeServiceLogger.logInfo("Using order referral fee", "order_referral", {
    referrerFeeRate,
    referrerId,
  });
}

export function logReferrerNotFound({ referralId }: { referralId: string }) {
  feeServiceLogger.logInfo(
    "Referrer with tg_id not found, adding full fee to marketplace",
    "referrer_not_found",
    { referralId }
  );
}

export function logTotalFeeApplied({
  feeAmount,
  feeType,
  referralFee,
  marketplaceFee,
}: {
  feeAmount: number;
  feeType: string;
  referralFee: number;
  marketplaceFee: number;
}) {
  feeServiceLogger.logInfo(`${feeType} fee applied`, "total_fee", {
    feeAmount,
    feeType,
    referralFee,
    marketplaceFee,
  });
}

export function logFeeServiceError({
  error,
  operation,
  userId,
  amount,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  amount?: number;
}) {
  feeServiceLogger.logError(
    `Error in fee service: ${operation}`,
    error,
    operation,
    {
      userId,
      amount,
    }
  );
}
