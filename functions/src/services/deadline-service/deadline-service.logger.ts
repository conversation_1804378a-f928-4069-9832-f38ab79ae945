import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const deadlineServiceLogger = createServiceLogger("deadline-service");

export function logDeadlineAdded({
  orderId,
  collectionId,
  deadline,
}: {
  orderId: string;
  collectionId: string;
  deadline: string;
}) {
  deadlineServiceLogger.logInfo(
    "Added dynamic deadline to order for MARKET collection",
    "deadline_added",
    {
      orderId,
      collectionId,
      deadline,
    }
  );
}

export function logNoDeadlineUpdatesNeeded({
  collectionId,
}: {
  collectionId: string;
}) {
  deadlineServiceLogger.logInfo(
    "No orders need deadline updates for collection",
    "no_updates_needed",
    {
      collectionId,
    }
  );
}

export function logDeadlineBatchProcessed({
  batchNumber,
  updatedCount,
  collectionId,
}: {
  batchNumber: number;
  updatedCount: number;
  collectionId: string;
}) {
  deadlineServiceLogger.logInfo(
    "Processed batch for deadline updates",
    "batch_processed",
    {
      batchNumber,
      updatedCount,
      collectionId,
    }
  );
}

export function logDeadlineUpdatesCompleted({
  totalUpdatedCount,
  collectionId,
}: {
  totalUpdatedCount: number;
  collectionId: string;
}) {
  deadlineServiceLogger.logInfo(
    "Added dynamic deadlines to orders for collection",
    "deadlines_completed",
    {
      totalUpdatedCount,
      collectionId,
    }
  );
}

export function logDeadlineServiceError({
  error,
  operation,
  orderId,
  collectionId,
}: {
  error: unknown;
  operation: string;
  orderId?: string;
  collectionId?: string;
}) {
  deadlineServiceLogger.logError(
    `Error in deadline service: ${operation}`,
    error,
    operation,
    {
      orderId,
      collectionId,
    }
  );
}
