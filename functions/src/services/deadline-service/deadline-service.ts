import * as admin from "firebase-admin";
import { getEffectiveLockPeriod } from "../../utils/lock-period-utils";
import { getAppConfig } from "../fee-service/fee-service";
import {
  logDeadlineAdded,
  logNoDeadlineUpdatesNeeded,
  logDeadlineBatchProcessed,
  logDeadlineUpdatesCompleted,
} from "./deadline-service.logger";
import {
  COLLECTION_NAME,
  CollectionEntity,
  firebaseTimestampToDate,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from "../../mikerudenko/marketplace-shared";

export const DEADLINE_DAYS = 7;
export const DEADLINE_MS = DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export const LIMITED_COLLECTION_DEADLINE_DAYS = 30;

export const LIMITED_COLLECTION_DEADLINE_MS =
  LIMITED_COLLECTION_DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export function createDeadline(days: number = DEADLINE_DAYS) {
  const deadlineMs = days * 24 * 60 * 60 * 1000;
  const deadline = new Date(Date.now() + deadlineMs);
  return admin.firestore.Timestamp.fromDate(deadline);
}

export function createStandardDeadline() {
  return createDeadline(DEADLINE_DAYS);
}

export function createLimitedCollectionDeadline() {
  return createDeadline(LIMITED_COLLECTION_DEADLINE_DAYS);
}

export async function addDeadlineIfMarketCollection(
  db: admin.firestore.Firestore,
  collectionId: string,
  orderId: string,
  updateData: any
) {
  const collectionDoc = await db
    .collection(COLLECTION_NAME)
    .doc(collectionId)
    .get();

  if (collectionDoc.exists) {
    const collection = collectionDoc.data() as CollectionEntity;
    if (collection.launchedAt) {
      // Use the new dynamic deadline calculation
      const deadline = await calculateOrderDeadline(collection);
      if (deadline) {
        updateData.deadline = deadline;
        logDeadlineAdded({
          orderId,
          collectionId,
          deadline: deadline.toDate().toISOString(),
        });
      }
    }
  }
}

export async function calculateOrderDeadline(
  collection: CollectionEntity | null
) {
  // If collection doesn't have launchedAt, set deadline to null
  if (!collection?.launchedAt) {
    return null;
  }

  const now = new Date();
  const launchedAt = firebaseTimestampToDate(collection.launchedAt);

  // Get app config to determine lock period
  const appConfig = await getAppConfig();

  // Get effective lock period (collection -> app config -> 21 days fallback)
  const lockPeriodDays = getEffectiveLockPeriod(collection, appConfig);
  const lockPeriodMs = lockPeriodDays * 24 * 60 * 60 * 1000;

  // Calculate freeze end date
  const freezeEndDate = new Date(launchedAt.getTime() + lockPeriodMs);

  // Calculate deadline based on purchase time relative to freeze period
  let deadlineDate: Date;

  if (now < freezeEndDate) {
    // Purchase during freeze period: deadline = freeze end + 7 days
    deadlineDate = new Date(freezeEndDate.getTime() + DEADLINE_MS);
  } else {
    // Purchase after freeze period: deadline = now + 7 days (minimum)
    deadlineDate = new Date(now.getTime() + DEADLINE_MS);
  }

  return admin.firestore.Timestamp.fromDate(deadlineDate);
}

export function isOrderExpired(deadline: admin.firestore.Timestamp) {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  return now > deadlineDate;
}

export async function getExpiredOrders(db: admin.firestore.Firestore) {
  const now = admin.firestore.Timestamp.now();

  const expiredOrdersQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("deadline", "<=", now)
    .where("status", "==", OrderStatus.PAID)
    .get();

  return expiredOrdersQuery.docs;
}

export async function addDeadlineToOrders(
  db: admin.firestore.Firestore,
  collectionId: string
) {
  const ordersQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("collectionId", "==", collectionId)
    .where("status", "==", OrderStatus.PAID)
    .where("deadline", "==", null)
    .get();

  const ordersToUpdate = ordersQuery.docs.filter((doc) => !doc.data().deadline);

  if (ordersToUpdate.length === 0) {
    logNoDeadlineUpdatesNeeded({ collectionId });
    return 0;
  }

  // Get collection data for dynamic deadline calculation
  const collectionDoc = await db
    .collection(COLLECTION_NAME)
    .doc(collectionId)
    .get();
  const collection = collectionDoc.exists
    ? (collectionDoc.data() as CollectionEntity)
    : null;

  const BATCH_SIZE = 400;
  let totalUpdatedCount = 0;

  for (let i = 0; i < ordersToUpdate.length; i += BATCH_SIZE) {
    const chunk = ordersToUpdate.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      // Use dynamic deadline calculation for each order
      const deadline = await calculateOrderDeadline(collection);
      if (deadline) {
        batch.update(orderDoc.ref, { deadline });
      }
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;

    logDeadlineBatchProcessed({
      batchNumber: Math.floor(i / BATCH_SIZE) + 1,
      updatedCount: chunk.length,
      collectionId,
    });
  }

  logDeadlineUpdatesCompleted({
    totalUpdatedCount,
    collectionId,
  });

  return totalUpdatedCount;
}

export function getTimeUntilDeadline(deadline: admin.firestore.Timestamp) {
  const now = new Date();
  const deadlineDate = deadline.toDate();
  const timeDiff = deadlineDate.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return { days: 0, hours: 0, minutes: 0, isExpired: true };
  }

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

  return { days, hours, minutes, isExpired: false };
}
