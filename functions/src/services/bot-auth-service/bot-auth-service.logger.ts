import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const botAuthServiceLogger = createServiceLogger("bot-auth-service");

export function logBotAuthSuccess({ operation }: { operation: string }) {
  botAuthServiceLogger.logInfo(
    "Bot authentication successful",
    "auth_success",
    {
      operation,
    }
  );
}

export function logBotAuthError({
  error,
  operation,
}: {
  error: unknown;
  operation: string;
}) {
  botAuthServiceLogger.logError(
    `Error in bot authentication: ${operation}`,
    error,
    operation,
    {}
  );
}
