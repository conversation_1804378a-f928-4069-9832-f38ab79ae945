import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const orderCancellationServiceLogger = createServiceLogger(
  "order-cancellation-service"
);

export function logOrderCancellationStarted({
  orderId,
  userId,
  operation,
}: {
  orderId: string;
  userId: string;
  operation: string;
}) {
  orderCancellationServiceLogger.logInfo(
    "Order cancellation started",
    "cancellation_started",
    {
      orderId,
      userId,
      operation,
    }
  );
}

export function logOrderCancellationCompleted({
  orderId,
  userId,
  feeApplied,
  feeType,
}: {
  orderId: string;
  userId: string;
  feeApplied: boolean;
  feeType?: string;
}) {
  orderCancellationServiceLogger.logInfo(
    "Order cancellation completed",
    "cancellation_completed",
    {
      orderId,
      userId,
      feeApplied,
      feeType,
    }
  );
}

export function logOrderCancellationError({
  error,
  operation,
  orderId,
  userId,
}: {
  error: unknown;
  operation: string;
  orderId?: string;
  userId?: string;
}) {
  orderCancellationServiceLogger.logError(
    `Error in order cancellation: ${operation}`,
    error,
    operation,
    {
      orderId,
      userId,
    }
  );
}
