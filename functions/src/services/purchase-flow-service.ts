/* eslint-disable indent */
import * as admin from "firebase-admin";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../constants/transaction-description-intl-keys";
import { lockFundsWithHistory } from "./balance-service/balance-service";
import {
  validateBuyerPurchase,
  validateSellerPurchase,
} from "./order-validation-service";
import { addDeadlineIfMarketCollection as addDeadlineIfCollectionIsLaunched } from "./deadline-service/deadline-service";
import {
  processFeesOnPurchase,
  processFeesAndTransferToSeller,
} from "./purchase-fee-processing-service";
import {
  UserType,
  TxType,
  OrderStatus,
  ORDERS_COLLECTION_NAME,
} from "../mikerudenko/marketplace-shared";

export async function processPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
    userType: UserType;
  }
) {
  const { userId, orderId, userType } = params;

  const validation =
    userType === UserType.BUYER
      ? await validateBuyerPurchase(db, { userId, orderId })
      : await validateSellerPurchase(db, { userId, orderId });

  const { order, lockedAmount, lockPercentage } = validation;

  // Apply purchase fees if this is a buyer purchase
  let feeResult = {
    totalFee: 0,
    referralFee: 0,
    marketplaceFee: 0,
    netAmountToSeller: 0,
  };
  if (userType === UserType.BUYER) {
    // Check if order has gift or giftId - if so, process fees and transfer to seller immediately
    if (order.giftId) {
      // Process fees and transfer net amount to seller since gift is already in relayer
      const fullFeeResult = await processFeesAndTransferToSeller(order, userId);
      feeResult = {
        totalFee: fullFeeResult.totalFee,
        referralFee: fullFeeResult.referralFee,
        marketplaceFee: fullFeeResult.marketplaceFee,
        netAmountToSeller: fullFeeResult.netAmountToSeller,
      };
    } else {
      // Regular fee processing without money transfer (will happen later in relayer)
      const regularFeeResult = await processFeesOnPurchase(order, userId);
      feeResult = {
        totalFee: regularFeeResult.totalFee,
        referralFee: regularFeeResult.referralFee,
        marketplaceFee: regularFeeResult.marketplaceFee,
        netAmountToSeller: regularFeeResult.netAmountToSeller,
      };
    }
  }

  // Lock funds for the user (skip for buyers when order has gift since funds are already transferred)
  const shouldLockFunds = !(userType === UserType.BUYER && order.giftId);

  if (shouldLockFunds) {
    const txType =
      userType === UserType.BUYER
        ? TxType.BUY_LOCK_COLLATERAL
        : TxType.SELL_LOCK_COLLATERAL;
    await lockFundsWithHistory({
      userId,
      amount: lockedAmount - feeResult.totalFee,
      txType,
      orderId,
      descriptionIntlKey:
        userType === UserType.BUYER
          ? TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_BUYER
          : TRANSACTION_DESCRIPTION_INTL_KEYS.LOCKED_COLLATERAL_FOR_SELLER,
      descriptionIntlParams: {
        amount: lockedAmount.toString(),
        percentage: (lockPercentage * 100).toString(),
        orderPrice: order.price.toString(),
      },
    });
  }

  // Prepare update data
  // If buyer is purchasing an order that already has gift or giftId,
  // set status directly to GIFT_SENT_TO_RELAYER
  const shouldSkipToPaidStatus = userType === UserType.BUYER && order.giftId;

  const updateData: any = {
    status: shouldSkipToPaidStatus
      ? OrderStatus.GIFT_SENT_TO_RELAYER
      : OrderStatus.PAID,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Set buyer or seller ID based on user type
  if (userType === UserType.BUYER) {
    updateData.buyerId = userId;
  } else {
    updateData.sellerId = userId;
  }

  // Add deadline if collection is in MARKET status
  await addDeadlineIfCollectionIsLaunched(
    db,
    order.collectionId,
    orderId,
    updateData
  );

  // Update order
  await db.collection(ORDERS_COLLECTION_NAME).doc(orderId).update(updateData);

  const actionMessage =
    userType === UserType.BUYER
      ? shouldSkipToPaidStatus
        ? "Gift is ready to be claimed from relayer."
        : "Waiting for seller to send gift."
      : "You can now send the gift.";

  const feeMessage =
    feeResult.totalFee > 0
      ? ` Purchase fee of ${feeResult.totalFee} TON applied.`
      : "";

  // Different message for orders with gifts where money is already transferred
  const lockMessage = shouldLockFunds
    ? `${lockedAmount} TON locked (${lockPercentage * 100}% of ${
        order.price
      } TON order).`
    : `Payment completed! ${feeResult.netAmountToSeller} TON transferred to seller.`;

  return {
    success: true,
    message: `Purchase successful! ${lockMessage}${feeMessage} ${actionMessage}`,
    lockedAmount: shouldLockFunds ? lockedAmount : 0,
    orderAmount: order.price,
    lockPercentage: lockPercentage * 100,
    feeApplied: feeResult.totalFee,
    referralFee: feeResult.referralFee,
    marketplaceFee: feeResult.marketplaceFee,
    netAmountToSeller: feeResult.netAmountToSeller,
  };
}
