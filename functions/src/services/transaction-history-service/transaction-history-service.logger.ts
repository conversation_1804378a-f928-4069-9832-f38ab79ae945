import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const transactionHistoryServiceLogger = createServiceLogger(
  "transaction-history-service"
);

export function logTransactionCreated({
  userId,
  txType,
  amount,
}: {
  userId: string;
  txType: string;
  amount: number;
}) {
  transactionHistoryServiceLogger.logInfo(
    "Transaction history record created",
    "transaction_created",
    {
      userId,
      txType,
      amount,
    }
  );
}

export function logTransactionHistoryError({
  error,
  operation,
  userId,
  txType,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  txType?: string;
}) {
  transactionHistoryServiceLogger.logError(
    `Error in transaction history service: ${operation}`,
    error,
    operation,
    {
      userId,
      txType,
    }
  );
}
