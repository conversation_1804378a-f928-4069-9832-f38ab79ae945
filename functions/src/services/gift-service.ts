import * as admin from "firebase-admin";
import {
  GiftEntity,
  GiftStatus,
  GIFTS_COLLECTION_NAME,
  AppDate,
  GiftFromBot,
} from "../mikerudenko/marketplace-shared";

export async function createGift(
  gift: GiftFromBot,
  ownerTgId: string,
  collectionId: string,
  batch?: admin.firestore.WriteBatch
) {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc();

  const giftEntity: GiftEntity = {
    id: giftRef.id,
    base_name: gift.base_name,
    owned_gift_id: gift.owned_gift_id,
    owner_tg_id: ownerTgId,
    collectionId,
    status: GiftStatus.DEPOSITED,
    backdrop: gift.backdrop,
    model: gift.model,
    symbol: gift.symbol,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.set(giftRef, giftEntity);
  } else {
    await giftRef.set(giftEntity);
  }

  return giftRef.id;
}

export async function updateGiftOwnership(
  giftId: string,
  newOwnerTgId: string,
  status: GiftStatus,
  batch?: admin.firestore.WriteBatch
): Promise<void> {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

  const updateData = {
    owner_tg_id: newOwnerTgId,
    status,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.update(giftRef, updateData);
  } else {
    await giftRef.update(updateData);
  }
}

export async function getGiftById(giftId: string) {
  const db = admin.firestore();
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();

  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}

export async function getGiftsByOwner(
  ownerTgId: string
): Promise<GiftEntity[]> {
  const db = admin.firestore();
  const snapshot = await db
    .collection(GIFTS_COLLECTION_NAME)
    .where("owner_tg_id", "==", ownerTgId)
    .get();

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  })) as GiftEntity[];
}

export async function markGiftAsWithdrawn(
  giftId: string,
  batch?: admin.firestore.WriteBatch
): Promise<void> {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

  const updateData = {
    status: GiftStatus.WITHDRAWN,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.update(giftRef, updateData);
  } else {
    await giftRef.update(updateData);
  }
}
