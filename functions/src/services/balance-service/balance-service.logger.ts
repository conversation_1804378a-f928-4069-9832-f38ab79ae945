import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const balanceServiceLogger = createServiceLogger("balance-service");

export function logBalanceUpdate({
  userId,
  sumChange,
  lockedChange,
  operation,
}: {
  userId: string;
  sumChange: number;
  lockedChange: number;
  operation: string;
}) {
  balanceServiceLogger.logInfo(
    `Balance updated for user ${userId}`,
    "balance_update",
    {
      userId,
      sumChange,
      lockedChange,
      operation,
    }
  );
}

export function logInsufficientFunds({
  userId,
  requiredAmount,
  availableBalance,
  operation,
}: {
  userId: string;
  requiredAmount: number;
  availableBalance: number;
  operation: string;
}) {
  balanceServiceLogger.logInfo(
    `Insufficient funds for user ${userId}`,
    "insufficient_funds",
    {
      userId,
      requiredAmount,
      availableBalance,
      operation,
    }
  );
}

export function logFundsValidated({
  userId,
  amount,
  operation,
}: {
  userId: string;
  amount: number;
  operation: string;
}) {
  balanceServiceLogger.logInfo(
    `Funds validated for user ${userId}`,
    "funds_validation",
    {
      userId,
      amount,
      operation,
    }
  );
}

export function logBalanceServiceError({
  error,
  operation,
  userId,
}: {
  error: unknown;
  operation: string;
  userId?: string;
}) {
  balanceServiceLogger.logError(
    `Error in balance service: ${operation}`,
    error,
    operation,
    {
      userId,
    }
  );
}
