import { createServiceLogger } from "../../logger/base-logger";

// Create service logger using functional composition
const tonWalletServiceLogger = createServiceLogger("ton-wallet-service");

export function logWalletValidation({
  walletAddress,
  isValid,
}: {
  walletAddress: string;
  isValid: boolean;
}) {
  tonWalletServiceLogger.logInfo(
    "TON wallet validation completed",
    "wallet_validation",
    {
      walletAddress,
      isValid,
    }
  );
}

export function logWalletBinding({
  userId,
  walletAddress,
}: {
  userId: string;
  walletAddress: string;
}) {
  tonWalletServiceLogger.logInfo("TON wallet bound to user", "wallet_binding", {
    userId,
    walletAddress,
  });
}

export function logTonWalletServiceError({
  error,
  operation,
  userId,
  walletAddress,
}: {
  error: unknown;
  operation: string;
  userId?: string;
  walletAddress?: string;
}) {
  tonWalletServiceLogger.logError(
    `Error in TON wallet service: ${operation}`,
    error,
    operation,
    {
      userId,
      walletAddress,
    }
  );
}
