import { logger } from "firebase-functions/v2";

export function logCreateSellOrderFromGiftFunctionCalled(context: {
  giftId?: string;
  price?: number;
  userId?: string;
}) {
  logger.info("Create sell order from gift function called", {
    operation: "create_sell_order_from_gift_start",
    giftId: context.giftId,
    price: context.price,
    userId: context.userId,
  });
}

export function logGiftValidation(context: {
  giftId: string;
  giftOwnerId: string;
  giftCollectionId: string;
}) {
  logger.info("Gift validation completed", {
    operation: "gift_validation",
    giftId: context.giftId,
    giftOwnerId: context.giftOwnerId,
    giftCollectionId: context.giftCollectionId,
  });
}

export function logCollectionValidation(context: {
  collectionId: string;
  collectionStatus: string;
  collectionActive: boolean;
}) {
  logger.info("Collection validation completed", {
    operation: "collection_validation",
    collectionId: context.collectionId,
    collectionStatus: context.collectionStatus,
    collectionActive: context.collectionActive,
  });
}

export function logOrderCreated(context: {
  orderId: string;
  orderNumber: number;
  giftId: string;
  userId: string;
  price: number;
  collectionId: string;
}) {
  logger.info("Sell order created from gift", {
    operation: "order_created_from_gift",
    orderId: context.orderId,
    orderNumber: context.orderNumber,
    giftId: context.giftId,
    userId: context.userId,
    price: context.price,
    collectionId: context.collectionId,
  });
}

export function logCreateSellOrderFromGiftCompleted(context: {
  giftId: string;
  price: number;
  userId: string;
  success: boolean;
  orderId?: string;
}) {
  logger.info("Create sell order from gift function completed", {
    operation: "create_sell_order_from_gift_complete",
    giftId: context.giftId,
    price: context.price,
    userId: context.userId,
    success: context.success,
    orderId: context.orderId,
  });
}

export function logCreateSellOrderFromGiftError(error: unknown) {
  logger.error("Error in create sell order from gift function", {
    operation: "create_sell_order_from_gift_error",
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
  });
}
