import { onCall, HttpsError } from "firebase-functions/v2/https";
import { createSellOrderFromGiftFlow } from "./create-sell-order-from-gift.service";
import {
  logCreateSellOrderFromGiftFunctionCalled,
  logCreateSellOrderFromGiftCompleted,
} from "./create-sell-order-from-gift.logger";
import { handleCreateSellOrderFromGiftError } from "./create-sell-order-from-gift.error-handler";
import { commonFunctionsConfig } from "../constants";

interface CreateSellOrderFromGiftRequest {
  giftId: string;
  price: number;
}

export const createSellOrderFromGift = onCall(
  commonFunctionsConfig,
  async (request) => {
    try {
      logCreateSellOrderFromGiftFunctionCalled({
        giftId: request.data?.giftId,
        price: request.data?.price,
        userId: request.auth?.uid,
      });

      // Validate request data
      if (!request.data) {
        throw new HttpsError("invalid-argument", "Request data is required");
      }

      if (!request.auth?.uid) {
        throw new HttpsError("unauthenticated", "User must be authenticated");
      }

      const { giftId, price } = request.data as CreateSellOrderFromGiftRequest;

      if (!giftId || !price) {
        throw new HttpsError(
          "invalid-argument",
          "giftId and price are required"
        );
      }

      if (price <= 0) {
        throw new HttpsError(
          "invalid-argument",
          "Price must be greater than 0"
        );
      }

      const result = await createSellOrderFromGiftFlow({
        giftId,
        price,
        userId: request.auth.uid,
      });

      logCreateSellOrderFromGiftCompleted({
        giftId,
        price,
        userId: request.auth.uid,
        success: result.success,
        orderId: result.order?.id,
      });

      return result;
    } catch (error) {
      return handleCreateSellOrderFromGiftError(error);
    }
  }
);
