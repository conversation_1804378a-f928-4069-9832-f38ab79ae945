import { HttpsError } from "firebase-functions/v2/https";
import { logCreateSellOrderFromGiftError } from "./create-sell-order-from-gift.logger";

export function throwGiftNotOwnedByUser(): never {
  throw new HttpsError(
    "permission-denied",
    "Gift does not belong to the current user"
  );
}

export function throwInvalidPrice(): never {
  throw new HttpsError(
    "invalid-argument",
    "Price must be greater than 0"
  );
}

export function throwCollectionNotFound(): never {
  throw new HttpsError("not-found", "Collection not found");
}

export function throwCollectionNotActive(): never {
  throw new HttpsError(
    "failed-precondition",
    "Collection is not active"
  );
}

export function throwGiftCollectionMismatch(): never {
  throw new HttpsError(
    "failed-precondition",
    "Gift and collection mismatch"
  );
}

export function handleCreateSellOrderFromGiftError(error: unknown) {
  logCreateSellOrderFromGiftError(error);

  if (error instanceof HttpsError) {
    return {
      success: false,
      message: error.message,
    };
  }

  if (error instanceof Error) {
    // Handle specific validation errors from gift validation
    if (error.message.includes("Gift not found")) {
      return {
        success: false,
        message: "Gift not found",
      };
    }
    if (error.message.includes("status")) {
      return {
        success: false,
        message: "Gift must have status 'deposited'",
      };
    }
    if (error.message.includes("already linked")) {
      return {
        success: false,
        message: "Gift is already linked to another order",
      };
    }
  }

  return {
    success: false,
    message: "An unexpected error occurred while creating sell order from gift",
  };
}
