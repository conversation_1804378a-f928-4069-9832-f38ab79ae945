import { createServiceLogger } from "../logger/base-logger";

const adminCollectionLogger = createServiceLogger("admin-collection-function");

export function logAdminCollectionOperation({
  operation,
  collectionId,
  updatedCount,
  userId,
}: {
  operation: string;
  collectionId: string;
  updatedCount: number;
  userId: string;
}) {
  adminCollectionLogger.logInfo(
    `Admin collection operation: ${operation} for collection ${collectionId}, updated ${updatedCount} orders`,
    operation,
    {
      collectionId,
      updatedCount,
      userId,
    }
  );
}

export function logAdminCollectionError({
  error,
  operation,
  collectionId,
  userId,
}: {
  error: unknown;
  operation: string;
  collectionId?: string;
  userId?: string;
}) {
  adminCollectionLogger.logError(
    `Error in admin collection operation: ${operation}`,
    error,
    operation,
    {
      collectionId,
      userId,
    }
  );
}
