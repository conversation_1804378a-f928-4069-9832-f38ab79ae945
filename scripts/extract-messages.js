const fs = require("fs");
const path = require("path");

const outDir = "src/locales";

// Define constants directly (since we can't import TS files in Node.js)
const PREM_CHANNEL = "@prem_channel";
const PREM_SUPPORT_OFFICIAL = "@prem_support_official";

function extractMessages() {
  const filePath = "src/intl/messages.ts";
  const contents = fs.readFileSync(filePath, "utf8");

  // Find the defineMessages block
  const defineMessagesMatch = contents.match(
    /export const botMessages = defineMessages\(\{([\s\S]*?)\}\);/
  );
  if (!defineMessagesMatch) {
    throw new Error("Could not find defineMessages block");
  }

  const messagesBlock = defineMessagesMatch[1];
  const results = [];

  // Split by message key pattern and process each message
  const messagePattern = /(\w+):\s*\{[\s\S]*?\},?(?=\s*(?:\w+:|\/\/|$))/g;
  let match;

  while ((match = messagePattern.exec(messagesBlock)) !== null) {
    const messageBlock = match[0];
    const messageKey = match[1];

    // Extract id
    const idMatch = messageBlock.match(/id:\s*["']([^"']+)["']/);
    if (!idMatch) continue;

    // Extract defaultMessage with better handling
    let defaultMessage = "";

    // Try template literal first (backticks)
    const templateLiteralMatch = messageBlock.match(
      /defaultMessage:\s*`([\s\S]*?)`(?:\s*,|\s*\})/
    );
    if (templateLiteralMatch) {
      defaultMessage = templateLiteralMatch[1];
      // Resolve template literals with constants
      defaultMessage = defaultMessage
        .replace(/\$\{PREM_CHANNEL\}/g, PREM_CHANNEL)
        .replace(/\$\{PREM_SUPPORT_OFFICIAL\}/g, PREM_SUPPORT_OFFICIAL)
        .replace(
          /\$\{PREM_SUPPORT_OFFICIAL\.substring\(1\)\}/g,
          PREM_SUPPORT_OFFICIAL.substring(1)
        );
    } else {
      // Try quoted string (single or double quotes)
      const quotedMatch = messageBlock.match(
        /defaultMessage:\s*["']([\s\S]*?)["'](?:\s*,|\s*\})/
      );
      if (quotedMatch) {
        defaultMessage = quotedMatch[1];
      } else {
        // Try multiline quoted string
        const multilineMatch = messageBlock.match(
          /defaultMessage:\s*\n\s*["']([\s\S]*?)["'](?:\s*,|\s*\})/
        );
        if (multilineMatch) {
          defaultMessage = multilineMatch[1];
        }
      }
    }

    if (idMatch[1] && defaultMessage) {
      results.push({
        id: idMatch[1],
        defaultMessage: defaultMessage
          .replace(/\\n/g, "\n")
          .replace(/\\'/g, "'")
          .replace(/\\"/g, '"')
          .trim(),
      });
    }
  }

  return results;
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

function generateLocaleFiles(messages) {
  ensureDirectoryExists(outDir);

  // Create English locale file (base)
  const englishMessages = {};
  messages.forEach((message) => {
    englishMessages[message.id] = message.defaultMessage;
  });

  // Sort keys alphabetically
  const sortedEnglish = Object.keys(englishMessages)
    .sort()
    .reduce((obj, key) => {
      obj[key] = englishMessages[key];
      return obj;
    }, {});

  // Write English locale
  fs.writeFileSync(
    path.join(outDir, "en.json"),
    JSON.stringify(sortedEnglish, null, 2)
  );

  // Create template files for other locales (ru.json, uk.json)
  // These will have empty strings for translators to fill in
  const emptyMessages = {};
  Object.keys(sortedEnglish).forEach((key) => {
    emptyMessages[key] = ""; // Empty string for translation
  });

  // Write Russian locale template
  fs.writeFileSync(
    path.join(outDir, "ru.json"),
    JSON.stringify(emptyMessages, null, 2)
  );

  // Write Ukrainian locale template
  fs.writeFileSync(
    path.join(outDir, "uk.json"),
    JSON.stringify(emptyMessages, null, 2)
  );

  console.log(
    `Generated ${Object.keys(sortedEnglish).length} messages in en.json`
  );
  console.log("Generated template files for ru.json and uk.json");
}

// Run the extraction
try {
  const messages = extractMessages();
  generateLocaleFiles(messages);
  console.log("Message extraction completed successfully!");
} catch (error) {
  console.error("Error during message extraction:", error.message);
  process.exit(1);
}
