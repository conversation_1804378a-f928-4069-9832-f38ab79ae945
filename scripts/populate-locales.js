const fs = require("fs");
const path = require("path");

const localesDir = "src/locales";

function populateLocales() {
  // Read English messages
  const englishMessages = JSON.parse(
    fs.readFileSync(path.join(localesDir, "en.json"), "utf8")
  );

  // Populate Russian locale with English messages as placeholders
  fs.writeFileSync(
    path.join(localesDir, "ru.json"),
    JSON.stringify(englishMessages, null, 2)
  );

  // Populate Ukrainian locale with English messages as placeholders
  fs.writeFileSync(
    path.join(localesDir, "uk.json"),
    JSON.stringify(englishMessages, null, 2)
  );

  console.log("Populated ru.json and uk.json with English placeholders");
  console.log("Translators can now replace the English text with translations");
}

// Run the population
try {
  populateLocales();
  console.log("Locale population completed successfully!");
} catch (error) {
  console.error("Error during locale population:", error.message);
  process.exit(1);
}
